<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindField Test Runner</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        
        .test-header {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .test-header h1 {
            color: #6c63ff;
            margin-bottom: 0.5rem;
        }
        
        .test-controls {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .test-controls button {
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }
        
        .test-results {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .test-category {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        
        .test-category h3 {
            margin-top: 0;
            color: #6c63ff;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 0.5rem;
        }
        
        .console-output {
            background: #1a1a1a;
            color: #00ff00;
            padding: 1rem;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .metric-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #6c63ff;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.25rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-passed { background-color: #4caf50; }
        .status-failed { background-color: #f44336; }
        .status-pending { background-color: #ff9800; }
        .status-running { background-color: #2196f3; animation: pulse 1s infinite; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🧪 MindField Test Suite</h1>
        <p>Comprehensive testing for form validation, error handling, accessibility, and performance</p>
    </div>

    <div class="test-controls">
        <h3>Test Controls</h3>
        <button onclick="runAllTests()" class="btn btn-primary">Run All Tests</button>
        <button onclick="runFormValidationTests()" class="btn btn-outline">Form Validation</button>
        <button onclick="runErrorHandlingTests()" class="btn btn-outline">Error Handling</button>
        <button onclick="runAccessibilityTests()" class="btn btn-outline">Accessibility</button>
        <button onclick="runPerformanceTests()" class="btn btn-outline">Performance</button>
        <button onclick="runIntegrationTests()" class="btn btn-outline">Integration</button>
        <button onclick="clearResults()" class="btn btn-secondary">Clear Results</button>
    </div>

    <div class="test-results">
        <h3>Test Results</h3>
        <div id="test-summary" class="performance-metrics">
            <div class="metric-card">
                <div class="metric-value" id="total-tests">0</div>
                <div class="metric-label">Total Tests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="passed-tests" style="color: #4caf50;">0</div>
                <div class="metric-label">Passed</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="failed-tests" style="color: #f44336;">0</div>
                <div class="metric-label">Failed</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="success-rate">0%</div>
                <div class="metric-label">Success Rate</div>
            </div>
        </div>
        
        <div id="test-details" style="margin-top: 2rem;"></div>
    </div>

    <div class="test-results">
        <h3>Performance Metrics</h3>
        <div id="performance-metrics" class="performance-metrics">
            <div class="metric-card">
                <div class="metric-value" id="load-time">-</div>
                <div class="metric-label">Page Load Time</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="dom-ready">-</div>
                <div class="metric-label">DOM Ready</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="memory-usage">-</div>
                <div class="metric-label">Memory Usage</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="test-duration">-</div>
                <div class="metric-label">Test Duration</div>
            </div>
        </div>
    </div>

    <div class="test-results">
        <h3>Console Output</h3>
        <div id="console-output" class="console-output">
            Test runner initialized. Click "Run All Tests" to begin testing.
        </div>
    </div>

    <!-- Include all necessary scripts -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    
    <!-- App Scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/error-handler.js"></script>
    <script src="js/form-validator.js"></script>
    <script src="js/accessibility.js"></script>
    <script src="js/performance.js"></script>
    <script src="js/auth.js"></script>
    
    <!-- Test Framework and Tests -->
    <script src="tests/test-framework.js"></script>
    <script src="tests/form-validation.test.js"></script>
    <script src="tests/error-handling.test.js"></script>
    <script src="tests/accessibility.test.js"></script>
    <script src="tests/performance.test.js"></script>
    <script src="tests/integration.test.js"></script>

    <script>
        // Performance monitoring
        const performanceMetrics = {
            startTime: performance.now(),
            domReady: null,
            loadTime: null
        };

        // Capture DOM ready time
        document.addEventListener('DOMContentLoaded', () => {
            performanceMetrics.domReady = performance.now();
            updatePerformanceDisplay();
        });

        // Capture load time
        window.addEventListener('load', () => {
            performanceMetrics.loadTime = performance.now();
            updatePerformanceDisplay();
        });

        // Console output capture
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const consoleOutput = document.getElementById('console-output');

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // Test runner functions
        async function runAllTests() {
            const startTime = performance.now();
            addToConsole('🚀 Starting comprehensive test suite...');
            
            try {
                await testFramework.runTests();
                updateTestSummary();
                
                const duration = performance.now() - startTime;
                document.getElementById('test-duration').textContent = `${duration.toFixed(2)}ms`;
                
                addToConsole(`✅ All tests completed in ${duration.toFixed(2)}ms`);
            } catch (error) {
                addToConsole(`❌ Test suite failed: ${error.message}`, 'error');
            }
        }

        async function runFormValidationTests() {
            addToConsole('🔍 Running form validation tests...');
            const formTests = testFramework.tests.filter(test => test.name.includes('Form Validator'));
            await runSpecificTests(formTests);
        }

        async function runErrorHandlingTests() {
            addToConsole('🛡️ Running error handling tests...');
            const errorTests = testFramework.tests.filter(test => test.name.includes('Error Handler'));
            await runSpecificTests(errorTests);
        }

        async function runAccessibilityTests() {
            addToConsole('♿ Running accessibility tests...');
            const a11yTests = testFramework.tests.filter(test => test.name.includes('Accessibility'));
            await runSpecificTests(a11yTests);
        }

        async function runPerformanceTests() {
            addToConsole('⚡ Running performance tests...');
            const perfTests = testFramework.tests.filter(test => test.name.includes('Performance'));
            await runSpecificTests(perfTests);
        }

        async function runIntegrationTests() {
            addToConsole('🔗 Running integration tests...');
            const integrationTests = testFramework.tests.filter(test => test.name.includes('Integration'));
            await runSpecificTests(integrationTests);
        }

        async function runSpecificTests(tests) {
            const startTime = performance.now();
            let passed = 0;
            let failed = 0;

            for (const test of tests) {
                try {
                    await test.testFunction();
                    test.status = 'passed';
                    passed++;
                    addToConsole(`✅ ${test.name}`);
                } catch (error) {
                    test.status = 'failed';
                    failed++;
                    addToConsole(`❌ ${test.name}: ${error.message}`, 'error');
                }
            }

            const duration = performance.now() - startTime;
            addToConsole(`📊 Category completed: ${passed} passed, ${failed} failed (${duration.toFixed(2)}ms)`);
            
            updateTestSummary();
        }

        function updateTestSummary() {
            const total = testFramework.tests.length;
            const passed = testFramework.tests.filter(t => t.status === 'passed').length;
            const failed = testFramework.tests.filter(t => t.status === 'failed').length;
            const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;

            document.getElementById('total-tests').textContent = total;
            document.getElementById('passed-tests').textContent = passed;
            document.getElementById('failed-tests').textContent = failed;
            document.getElementById('success-rate').textContent = `${successRate}%`;

            updateTestDetails();
        }

        function updateTestDetails() {
            const detailsContainer = document.getElementById('test-details');
            const categories = {
                'Form Validation': testFramework.tests.filter(t => t.name.includes('Form Validator')),
                'Error Handling': testFramework.tests.filter(t => t.name.includes('Error Handler')),
                'Accessibility': testFramework.tests.filter(t => t.name.includes('Accessibility')),
                'Performance': testFramework.tests.filter(t => t.name.includes('Performance')),
                'Integration': testFramework.tests.filter(t => t.name.includes('Integration'))
            };

            let html = '';
            Object.entries(categories).forEach(([category, tests]) => {
                if (tests.length > 0) {
                    html += `<div class="test-category">
                        <h3>${category}</h3>`;
                    
                    tests.forEach(test => {
                        const statusClass = `status-${test.status || 'pending'}`;
                        html += `<div style="margin-bottom: 0.5rem;">
                            <span class="status-indicator ${statusClass}"></span>
                            <strong>${test.name}</strong>
                            ${test.message ? `<br><small style="margin-left: 1.5rem; color: #666;">${test.message}</small>` : ''}
                        </div>`;
                    });
                    
                    html += '</div>';
                }
            });

            detailsContainer.innerHTML = html;
        }

        function updatePerformanceDisplay() {
            if (performanceMetrics.domReady) {
                document.getElementById('dom-ready').textContent = `${performanceMetrics.domReady.toFixed(2)}ms`;
            }
            if (performanceMetrics.loadTime) {
                document.getElementById('load-time').textContent = `${performanceMetrics.loadTime.toFixed(2)}ms`;
            }
            
            // Memory usage (if available)
            if (performance.memory) {
                const memoryMB = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
                document.getElementById('memory-usage').textContent = `${memoryMB}MB`;
            }
        }

        function clearResults() {
            consoleOutput.textContent = 'Console cleared. Ready for new tests.\n';
            document.getElementById('test-details').innerHTML = '';
            
            // Reset test statuses
            testFramework.tests.forEach(test => {
                test.status = 'pending';
                test.message = '';
            });
            
            updateTestSummary();
            addToConsole('🧹 Results cleared. Ready for testing.');
        }

        // Initialize display
        updateTestSummary();
        updatePerformanceDisplay();
        
        addToConsole('🎯 MindField Test Runner initialized successfully!');
        addToConsole('📋 Available test categories:');
        addToConsole('   • Form Validation (email, password, required fields)');
        addToConsole('   • Error Handling (notifications, loading states)');
        addToConsole('   • Accessibility (ARIA, keyboard navigation, screen readers)');
        addToConsole('   • Performance (debouncing, caching, lazy loading)');
        addToConsole('   • Integration (end-to-end workflows)');
        addToConsole('');
        addToConsole('Click "Run All Tests" to begin comprehensive testing.');
    </script>
</body>
</html>
