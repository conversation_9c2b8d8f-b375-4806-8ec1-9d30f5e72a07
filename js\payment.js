// Payment functionality for MindField using Stripe

// Initialize Stripe with your publishable key
// Replace with your actual Stripe publishable key
const stripePublicKey = 'pk_test_51RD2VNH6ezcZslsKK3hsRvpMwsBw1S3ZL5VtxevSLKIWZHqvnPZdYwUSFapDytJgVIIk2gyROMQkO1SDCjdBpLOu001kutKDIB';
const stripe = Stripe(stripePublicKey);

// Initialize Stripe Elements
let elements;
let card;
let cardErrors;

// Initialize payment form
function initPaymentForm() {
  // Create Stripe Elements instance
  elements = stripe.elements();
  
  // Create card Element
  card = elements.create('card', {
    style: {
      base: {
        color: '#32325d',
        fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
        fontSmoothing: 'antialiased',
        fontSize: '16px',
        '::placeholder': {
          color: '#aab7c4'
        }
      },
      invalid: {
        color: '#fa755a',
        iconColor: '#fa755a'
      }
    }
  });
  
  // Mount card Element
  const cardElement = document.getElementById('card-element');
  if (cardElement) {
    card.mount('#card-element');
    
    // Handle card errors
    cardErrors = document.getElementById('card-errors');
    card.addEventListener('change', function(event) {
      if (event.error) {
        cardErrors.textContent = event.error.message;
      } else {
        cardErrors.textContent = '';
      }
    });
    
    // Handle form submission
    const paymentForm = document.getElementById('payment-form');
    if (paymentForm) {
      paymentForm.addEventListener('submit', handlePaymentSubmit);
    }
  }
}

// Handle payment form submission
async function handlePaymentSubmit(event) {
  event.preventDefault();
  
  // Get booking details from form or data attributes
  const form = event.target;
  const amount = form.getAttribute('data-amount');
  const currency = form.getAttribute('data-currency') || 'usd';
  const description = form.getAttribute('data-description');
  const retreatId = form.getAttribute('data-retreat-id');
  const startDate = form.getAttribute('data-start-date');
  const endDate = form.getAttribute('data-end-date');
  
  // Disable submit button to prevent multiple submissions
  const submitButton = form.querySelector('button[type="submit"]');
  submitButton.disabled = true;
  submitButton.textContent = 'Processing...';
  
  try {
    // Create payment intent on server
    const paymentIntentResponse = await createPaymentIntent(amount, currency, description);
    const clientSecret = paymentIntentResponse.clientSecret;
    
    // Confirm card payment
    const result = await stripe.confirmCardPayment(clientSecret, {
      payment_method: {
        card: card,
        billing_details: {
          name: document.getElementById('cardholder-name').value
        }
      }
    });
    
    if (result.error) {
      // Show error message
      showPaymentError(result.error.message);
      
      // Re-enable submit button
      submitButton.disabled = false;
      submitButton.textContent = 'Pay Now';
    } else {
      // Payment succeeded
      if (result.paymentIntent.status === 'succeeded') {
        // Create booking in database
        const bookingData = {
          userId: auth.currentUser.uid,
          retreatId: retreatId,
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          amount: amount,
          currency: currency,
          paymentIntentId: result.paymentIntent.id,
          status: 'confirmed'
        };
        
        await createBooking(bookingData);
        
        // Show success message and redirect
        showPaymentSuccess();
        
        // Redirect to booking confirmation page after 2 seconds
        setTimeout(() => {
          window.location.href = `booking-confirmation.html?id=${bookingData.id}`;
        }, 2000);
      }
    }
  } catch (error) {
    console.error('Payment error:', error);
    showPaymentError('An error occurred while processing your payment. Please try again.');
    
    // Re-enable submit button
    submitButton.disabled = false;
    submitButton.textContent = 'Pay Now';
  }
}

// Create payment intent on server
async function createPaymentIntent(amount, currency, description) {
  // In a real app, this would be a server call
  // For demo purposes, we'll simulate a successful response
  
  // This would normally be a fetch to your server endpoint
  // return fetch('/create-payment-intent', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json'
  //   },
  //   body: JSON.stringify({
  //     amount,
  //     currency,
  //     description
  //   })
  // }).then(response => response.json());
  
  // Simulated response
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        clientSecret: 'pi_test_secret_key',
        amount: amount,
        currency: currency
      });
    }, 1000);
  });
}

// Show payment error
function showPaymentError(message) {
  const errorElement = document.getElementById('payment-message');
  if (errorElement) {
    errorElement.className = 'payment-message error';
    errorElement.textContent = message;
    errorElement.style.display = 'block';
  }
}

// Show payment success
function showPaymentSuccess() {
  const messageElement = document.getElementById('payment-message');
  if (messageElement) {
    messageElement.className = 'payment-message success';
    messageElement.textContent = 'Payment successful! Redirecting to confirmation page...';
    messageElement.style.display = 'block';
  }
}

// Calculate booking total
function calculateBookingTotal(pricePerNight, startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const nights = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
  
  return pricePerNight * nights;
}

// Format currency
function formatCurrency(amount, currency = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
}

// Initialize payment methods page
function initPaymentMethodsPage() {
  // Get saved payment methods
  const paymentMethodsList = document.getElementById('payment-methods-list');
  
  if (paymentMethodsList) {
    // In a real app, you would fetch the user's saved payment methods from your server
    // For demo purposes, we'll show a sample payment method
    
    const sampleMethod = document.createElement('div');
    sampleMethod.className = 'payment-method-item';
    sampleMethod.innerHTML = `
      <div class="payment-method-icon">
        <i class="fab fa-cc-visa"></i>
      </div>
      <div class="payment-method-details">
        <h3>Visa ending in 4242</h3>
        <p>Expires 12/2025</p>
      </div>
      <div class="payment-method-actions">
        <button class="btn-text delete-payment-method">Remove</button>
        <button class="btn-text set-default-payment-method">Set as Default</button>
      </div>
    `;
    
    paymentMethodsList.appendChild(sampleMethod);
    
    // Add new payment method button
    const addButton = document.createElement('button');
    addButton.className = 'btn btn-outline add-payment-method';
    addButton.innerHTML = '<i class="fas fa-plus"></i> Add Payment Method';
    
    addButton.addEventListener('click', () => {
      // Show add payment method form
      document.getElementById('add-payment-method-form').style.display = 'block';
      initPaymentForm();
    });
    
    paymentMethodsList.appendChild(addButton);
  }
}

// Export payment functions
const paymentService = {
  initPaymentForm,
  calculateBookingTotal,
  formatCurrency,
  initPaymentMethodsPage
};
