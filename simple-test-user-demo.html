<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test User Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #6c63ff;
        }
        
        p {
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Test User Demo</h1>
        <p>This page is used to test the simple-test-user.js functionality.</p>
        <p>You should see a pink circular button in the bottom-right corner of the page.</p>
        <p>Click on it to open the test user panel.</p>
    </div>

    <script>
        console.log('Page loaded');
        
        // Add a simple test function to check if the test user panel is visible
        function checkTestUserPanel() {
            const panel = document.querySelector('.simple-test-user-panel');
            if (panel) {
                console.log('Simple test user panel found:', panel);
            } else {
                console.log('Simple test user panel not found');
            }
        }
        
        // Check after 2 seconds
        setTimeout(checkTestUserPanel, 2000);
    </script>
    <script src="js/simple-test-user.js"></script>
</body>
</html>
