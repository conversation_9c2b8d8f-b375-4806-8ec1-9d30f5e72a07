// Performance optimizations for MindField

document.addEventListener('DOMContentLoaded', function() {
  // Initialize performance optimizations
  initPerformanceOptimizations();
});

// Initialize performance optimizations
function initPerformanceOptimizations() {
  // Lazy load images
  lazyLoadImages();
  
  // Implement debounce for search and filter functions
  implementDebounce();
  
  // Cache DOM elements
  cacheElements();
  
  // Optimize event listeners
  optimizeEventListeners();
  
  // Implement virtual scrolling for long lists
  implementVirtualScrolling();
  
  // Preload critical resources
  preloadCriticalResources();
  
  // Implement local caching
  implementLocalCaching();
}

// Lazy load images
function lazyLoadImages() {
  // Check if IntersectionObserver is supported
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          const src = img.getAttribute('data-src');
          
          if (src) {
            img.src = src;
            img.removeAttribute('data-src');
          }
          
          observer.unobserve(img);
        }
      });
    });
    
    // Target all images with data-src attribute
    const lazyImages = document.querySelectorAll('img[data-src]');
    lazyImages.forEach(img => {
      imageObserver.observe(img);
    });
  } else {
    // Fallback for browsers that don't support IntersectionObserver
    const lazyImages = document.querySelectorAll('img[data-src]');
    
    function lazyLoad() {
      const scrollTop = window.pageYOffset;
      
      lazyImages.forEach(img => {
        if (img.offsetTop < window.innerHeight + scrollTop) {
          const src = img.getAttribute('data-src');
          
          if (src) {
            img.src = src;
            img.removeAttribute('data-src');
          }
        }
      });
      
      // If all images are loaded, remove the scroll event listener
      if (lazyImages.length === 0) {
        window.removeEventListener('scroll', lazyLoad);
      }
    }
    
    // Load initial images
    lazyLoad();
    
    // Add scroll event listener
    window.addEventListener('scroll', lazyLoad);
  }
}

// Implement debounce for search and filter functions
function implementDebounce() {
  // Debounce function
  window.debounce = function(func, wait, immediate) {
    let timeout;
    
    return function() {
      const context = this;
      const args = arguments;
      
      const later = function() {
        timeout = null;
        if (!immediate) func.apply(context, args);
      };
      
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      
      if (callNow) func.apply(context, args);
    };
  };
  
  // Apply debounce to search inputs
  const searchInputs = document.querySelectorAll('input[type="search"], .search-input');
  searchInputs.forEach(input => {
    const originalHandler = input.oninput;
    
    if (originalHandler) {
      input.oninput = window.debounce(originalHandler, 300);
    }
  });
  
  // Apply debounce to filter inputs
  const filterInputs = document.querySelectorAll('.filter-input, .filter-select, .filter-checkbox');
  filterInputs.forEach(input => {
    const originalHandler = input.onchange;
    
    if (originalHandler) {
      input.onchange = window.debounce(originalHandler, 300);
    }
  });
  
  // Apply debounce to price range slider
  const priceRange = document.getElementById('price-range');
  if (priceRange) {
    const originalHandler = priceRange.oninput;
    
    if (originalHandler) {
      priceRange.oninput = window.debounce(originalHandler, 100);
    }
  }
}

// Cache DOM elements
function cacheElements() {
  // Create a cache object
  window.elementCache = {};
  
  // Cache frequently accessed elements
  const elementsToCache = [
    'header',
    'footer',
    'main',
    '.navbar',
    '.profile-sidebar',
    '.results-list',
    '#map',
    '.filters-sidebar',
    '#browse-title',
    '.profile-content'
  ];
  
  elementsToCache.forEach(selector => {
    const element = document.querySelector(selector);
    if (element) {
      window.elementCache[selector] = element;
    }
  });
  
  // Create optimized querySelector function
  window.getElement = function(selector) {
    // Check if element is in cache
    if (window.elementCache[selector]) {
      return window.elementCache[selector];
    }
    
    // If not in cache, query and cache it
    const element = document.querySelector(selector);
    if (element) {
      window.elementCache[selector] = element;
    }
    
    return element;
  };
  
  // Create optimized querySelectorAll function
  window.getElements = function(selector) {
    // For collections, we don't cache to ensure fresh results
    return document.querySelectorAll(selector);
  };
}

// Optimize event listeners
function optimizeEventListeners() {
  // Use event delegation for common actions
  document.addEventListener('click', function(e) {
    // Handle button clicks
    if (e.target.classList.contains('btn') || e.target.closest('.btn')) {
      const button = e.target.classList.contains('btn') ? e.target : e.target.closest('.btn');
      
      // Add ripple effect
      const ripple = document.createElement('span');
      ripple.classList.add('ripple-effect');
      
      const rect = button.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      
      ripple.style.width = ripple.style.height = `${size}px`;
      ripple.style.left = `${e.clientX - rect.left - size/2}px`;
      ripple.style.top = `${e.clientY - rect.top - size/2}px`;
      
      button.appendChild(ripple);
      
      setTimeout(() => {
        ripple.remove();
      }, 600);
    }
    
    // Handle tab switching
    if (e.target.classList.contains('auth-tab') || e.target.closest('.auth-tab')) {
      const tab = e.target.classList.contains('auth-tab') ? e.target : e.target.closest('.auth-tab');
      const tabName = tab.getAttribute('data-tab');
      
      if (tabName && window.showAuthForm) {
        window.showAuthForm(tabName);
      }
    }
    
    // Handle profile tab switching
    if (e.target.classList.contains('profile-nav-item') || e.target.closest('.profile-nav-item')) {
      const navItem = e.target.classList.contains('profile-nav-item') ? e.target : e.target.closest('.profile-nav-item');
      const tabId = navItem.getAttribute('data-tab');
      
      if (tabId) {
        // Remove active class from all nav items
        const navItems = document.querySelectorAll('.profile-nav-item');
        navItems.forEach(item => item.classList.remove('active'));
        
        // Add active class to clicked nav item
        navItem.classList.add('active');
        
        // Hide all tabs
        const tabs = document.querySelectorAll('.profile-tab');
        tabs.forEach(tab => tab.classList.remove('active'));
        
        // Show selected tab
        const selectedTab = document.getElementById(tabId);
        if (selectedTab) {
          selectedTab.classList.add('active');
        }
      }
    }
  });
  
  // Use passive event listeners for scroll events
  window.addEventListener('scroll', function() {
    // Handle scroll-based animations
    const animatedElements = document.querySelectorAll('.animate-in:not(.visible)');
    
    animatedElements.forEach(element => {
      const rect = element.getBoundingClientRect();
      const isVisible = rect.top <= window.innerHeight * 0.8;
      
      if (isVisible) {
        element.classList.add('visible');
      }
    });
  }, { passive: true });
}

// Implement virtual scrolling for long lists
function implementVirtualScrolling() {
  const resultsList = document.getElementById('results-list');
  
  if (resultsList && resultsList.children.length > 20) {
    // Create a virtual scroller
    const virtualScroller = {
      container: resultsList,
      items: Array.from(resultsList.children),
      itemHeight: 0,
      visibleItems: 10,
      totalItems: 0,
      scrollPosition: 0,
      
      init: function() {
        // Calculate item height
        if (this.items.length > 0) {
          this.itemHeight = this.items[0].offsetHeight;
        }
        
        this.totalItems = this.items.length;
        
        // Create placeholder for scroll height
        const placeholder = document.createElement('div');
        placeholder.className = 'virtual-scroll-placeholder';
        placeholder.style.height = `${this.totalItems * this.itemHeight}px`;
        
        // Clear container and add placeholder
        this.container.innerHTML = '';
        this.container.appendChild(placeholder);
        
        // Add scroll event listener
        this.container.addEventListener('scroll', this.onScroll.bind(this), { passive: true });
        
        // Initial render
        this.render();
      },
      
      onScroll: function() {
        this.scrollPosition = this.container.scrollTop;
        this.render();
      },
      
      render: function() {
        // Calculate visible range
        const startIndex = Math.floor(this.scrollPosition / this.itemHeight) - 5;
        const endIndex = startIndex + this.visibleItems + 10;
        
        // Clear visible items
        const visibleContainer = this.container.querySelector('.virtual-scroll-visible') || document.createElement('div');
        visibleContainer.className = 'virtual-scroll-visible';
        visibleContainer.style.transform = `translateY(${Math.max(0, startIndex) * this.itemHeight}px)`;
        
        // Add visible items
        visibleContainer.innerHTML = '';
        
        for (let i = Math.max(0, startIndex); i < Math.min(this.totalItems, endIndex); i++) {
          visibleContainer.appendChild(this.items[i].cloneNode(true));
        }
        
        // Add visible container if not already added
        if (!this.container.querySelector('.virtual-scroll-visible')) {
          this.container.appendChild(visibleContainer);
        }
      }
    };
    
    // Initialize virtual scroller
    virtualScroller.init();
  }
}

// Preload critical resources
function preloadCriticalResources() {
  // Preload critical images
  const criticalImages = [
    // Add paths to critical images here
  ];
  
  criticalImages.forEach(src => {
    const img = new Image();
    img.src = src;
  });
  
  // Preload critical CSS
  const criticalCSS = [
    // Add paths to critical CSS files here
  ];
  
  criticalCSS.forEach(href => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'style';
    link.href = href;
    document.head.appendChild(link);
  });
  
  // Preload critical JavaScript
  const criticalJS = [
    // Add paths to critical JS files here
  ];
  
  criticalJS.forEach(src => {
    const script = document.createElement('link');
    script.rel = 'preload';
    script.as = 'script';
    script.href = src;
    document.head.appendChild(script);
  });
}

// Implement local caching
function implementLocalCaching() {
  // Create a cache object
  window.dataCache = {
    // Set cache item with expiration
    setItem: function(key, value, expirationMinutes = 30) {
      const item = {
        value: value,
        expiration: Date.now() + (expirationMinutes * 60 * 1000)
      };
      
      localStorage.setItem(`cache_${key}`, JSON.stringify(item));
    },
    
    // Get cache item
    getItem: function(key) {
      const item = localStorage.getItem(`cache_${key}`);
      
      if (!item) {
        return null;
      }
      
      const parsedItem = JSON.parse(item);
      
      // Check if item is expired
      if (Date.now() > parsedItem.expiration) {
        localStorage.removeItem(`cache_${key}`);
        return null;
      }
      
      return parsedItem.value;
    },
    
    // Remove cache item
    removeItem: function(key) {
      localStorage.removeItem(`cache_${key}`);
    },
    
    // Clear all cache items
    clear: function() {
      // Get all cache keys
      const cacheKeys = [];
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        
        if (key.startsWith('cache_')) {
          cacheKeys.push(key);
        }
      }
      
      // Remove all cache items
      cacheKeys.forEach(key => {
        localStorage.removeItem(key);
      });
    },
    
    // Clear expired cache items
    clearExpired: function() {
      // Get all cache keys
      const cacheKeys = [];
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        
        if (key.startsWith('cache_')) {
          cacheKeys.push(key);
        }
      }
      
      // Check each cache item
      cacheKeys.forEach(key => {
        const item = localStorage.getItem(key);
        
        if (item) {
          const parsedItem = JSON.parse(item);
          
          // Remove if expired
          if (Date.now() > parsedItem.expiration) {
            localStorage.removeItem(key);
          }
        }
      });
    }
  };
  
  // Clear expired cache items on load
  window.dataCache.clearExpired();
}

// Implement request batching
window.batchRequests = (function() {
  const queue = [];
  let timeout = null;
  
  return function(request, callback) {
    // Add request to queue
    queue.push({
      request: request,
      callback: callback
    });
    
    // Clear existing timeout
    if (timeout) {
      clearTimeout(timeout);
    }
    
    // Set new timeout to process queue
    timeout = setTimeout(function() {
      // Process queue
      const batchedRequests = queue.map(item => item.request);
      const callbacks = queue.map(item => item.callback);
      
      // Clear queue
      queue.length = 0;
      
      // Make batched request
      processBatchedRequests(batchedRequests)
        .then(results => {
          // Call callbacks with results
          results.forEach((result, index) => {
            if (callbacks[index]) {
              callbacks[index](result);
            }
          });
        })
        .catch(error => {
          console.error('Batched request error:', error);
        });
    }, 50);
  };
})();

// Process batched requests
function processBatchedRequests(requests) {
  // In a real app, this would send a single request to the server
  // For demo purposes, we'll process each request individually
  return Promise.all(requests.map(request => {
    // Process individual request
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({ success: true, data: {} });
      }, 100);
    });
  }));
}
