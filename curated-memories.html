<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindField - Curated Memories</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Curated Memories Specific Styles */
        :root {
            --memory-gold: #d4af37;
            --memory-sage: #9caf88;
            --memory-lavender: #b19cd9;
            --memory-coral: #ff7f7f;
            --memory-mist: #f8f9fa;
            --memory-shadow: rgba(0, 0, 0, 0.08);
        }

        .memories-hero {
            background: linear-gradient(135deg, var(--memory-lavender) 0%, var(--primary-color) 50%, var(--memory-sage) 100%);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .memories-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>');
            opacity: 0.3;
        }

        .memories-hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .memories-hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            font-weight: 300;
            letter-spacing: 2px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .memories-hero .subtitle {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            font-style: italic;
            opacity: 0.9;
        }

        .memories-hero .description {
            font-size: 1.1rem;
            line-height: 1.8;
            opacity: 0.8;
            max-width: 600px;
            margin: 0 auto;
        }

        .soul-whispers-badge {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            font-size: 0.9rem;
            margin-top: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .memories-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 80px 2rem;
        }

        .memory-story {
            margin-bottom: 120px;
            position: relative;
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s ease;
        }

        .memory-story.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .memory-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .memory-date {
            display: inline-block;
            background: var(--memory-gold);
            color: white;
            padding: 0.5rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
            letter-spacing: 1px;
            margin-bottom: 1rem;
        }

        .memory-title {
            font-size: 2.8rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-weight: 300;
            line-height: 1.2;
        }

        .memory-location {
            font-size: 1.2rem;
            color: var(--memory-sage);
            font-style: italic;
            margin-bottom: 2rem;
        }

        .memory-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: start;
            margin-bottom: 60px;
        }

        .memory-story:nth-child(even) .memory-content {
            direction: rtl;
        }

        .memory-story:nth-child(even) .memory-content > * {
            direction: ltr;
        }

        .memory-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: var(--text-color);
        }

        .memory-text .opening {
            font-size: 1.3rem;
            font-style: italic;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            position: relative;
            padding-left: 2rem;
        }

        .memory-text .opening::before {
            content: '"';
            position: absolute;
            left: 0;
            top: -10px;
            font-size: 3rem;
            color: var(--memory-lavender);
            opacity: 0.5;
        }

        .memory-text p {
            margin-bottom: 1.5rem;
        }

        .memory-text .highlight {
            background: linear-gradient(120deg, transparent 0%, var(--memory-mist) 50%, transparent 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: 500;
        }

        .memory-visual {
            position: relative;
        }

        .memory-image {
            width: 100%;
            border-radius: 15px;
            box-shadow: 0 20px 40px var(--memory-shadow);
            transition: transform 0.3s ease;
        }

        .memory-image:hover {
            transform: scale(1.02);
        }

        .memory-caption {
            position: absolute;
            bottom: -15px;
            left: 20px;
            right: 20px;
            background: white;
            padding: 1rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px var(--memory-shadow);
            font-size: 0.9rem;
            font-style: italic;
            color: var(--text-light);
            text-align: center;
        }

        .testimonials {
            background: var(--memory-mist);
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
        }

        .testimonials-title {
            text-align: center;
            font-size: 1.5rem;
            color: var(--primary-color);
            margin-bottom: 30px;
            font-weight: 300;
        }

        .testimonial-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .testimonial {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            position: relative;
        }

        .testimonial::before {
            content: '"';
            position: absolute;
            top: -10px;
            left: 20px;
            font-size: 3rem;
            color: var(--memory-coral);
            opacity: 0.3;
        }

        .testimonial-text {
            font-style: italic;
            line-height: 1.6;
            margin-bottom: 15px;
            color: var(--text-color);
        }

        .testimonial-author {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 0.9rem;
        }

        .memory-divider {
            text-align: center;
            margin: 80px 0;
        }

        .memory-divider::before {
            content: '✦ ✦ ✦';
            color: var(--memory-gold);
            font-size: 1.5rem;
            letter-spacing: 1rem;
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(100vh) rotate(0deg); }
            100% { transform: translateY(-100px) rotate(360deg); }
        }

        .floating-element:nth-child(1) { left: 10%; animation-delay: 0s; }
        .floating-element:nth-child(2) { left: 30%; animation-delay: 5s; }
        .floating-element:nth-child(3) { left: 50%; animation-delay: 10s; }
        .floating-element:nth-child(4) { left: 70%; animation-delay: 15s; }
        .floating-element:nth-child(5) { left: 90%; animation-delay: 20s; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .memories-hero h1 {
                font-size: 2.5rem;
            }

            .memory-content {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .memory-story:nth-child(even) .memory-content {
                direction: ltr;
            }

            .memory-title {
                font-size: 2.2rem;
            }

            .testimonial-grid {
                grid-template-columns: 1fr;
            }

            .memories-container {
                padding: 60px 1rem;
            }
        }

        /* Loading Animation */
        .memory-story.loading {
            opacity: 0.5;
        }

        .memory-story.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 40px;
            height: 40px;
            border: 3px solid var(--memory-lavender);
            border-top: 3px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            transform: translate(-50%, -50%);
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
    </style>
</head>
<body class="memories-page">
    <header>
        <nav class="navbar">
            <div class="logo"><a href="index.html">MindField</a></div>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="about.html">About</a>
                <a href="curated-memories.html" class="active">Curated</a>
                <a href="groups.html">Groups</a>
                <a href="browse.html">Browse</a>
                <a href="index.html#contact">Contact</a>
            </div>
            <div class="profile-icon">
                <a href="login.html"><i class="fas fa-user-circle"></i></a>
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <main>
        <section class="memories-hero">
            <div class="floating-elements">
                <div class="floating-element">🌸</div>
                <div class="floating-element">✨</div>
                <div class="floating-element">🍃</div>
                <div class="floating-element">🌙</div>
                <div class="floating-element">⭐</div>
            </div>
            <div class="memories-hero-content">
                <h1 class="animate-in">Curated Memories</h1>
                <p class="subtitle animate-in">Where Transformation Becomes Story</p>
                <p class="description animate-in">
                    Each month, we gather the whispers of souls touched by retreat magic. These are not mere recollections,
                    but living tapestries woven from moments of profound awakening, captured in our signature storytelling style.
                </p>
                <div class="soul-whispers-badge animate-in">
                    Written in Soul Whispers™ Style
                </div>
            </div>
        </section>

        <div class="memories-container">
            <!-- December 2024 Story -->
            <article class="memory-story" data-month="december-2024">
                <div class="memory-header">
                    <div class="memory-date">December 2024</div>
                    <h2 class="memory-title">The Silence That Spoke Volumes</h2>
                    <div class="memory-location">🏔️ Himalayan Foothills, Nepal</div>
                </div>

                <div class="memory-content">
                    <div class="memory-text">
                        <p class="opening">In the thin air where earth kisses sky, twelve souls discovered that silence has its own language.</p>

                        <p>The retreat began as most do—with nervous laughter and the rustle of expectations. But by the third dawn, something had shifted. <span class="highlight">The mountain had begun its ancient work of stripping away everything unnecessary.</span></p>

                        <p>Elena, a marketing executive from London, later described it as "the moment I remembered who I was before the world told me who to be." She had come seeking stress relief but found something far more precious—the courage to listen to her own heart's whispers.</p>

                        <p>The daily rhythm was simple: sunrise meditation facing the peaks, mindful walking through rhododendron forests, and evening circles where stories emerged like butterflies from cocoons. <span class="highlight">Each participant became both student and teacher, mirror and reflection.</span></p>

                        <p>On the final morning, as prayer flags danced in the mountain breeze, the group sat in perfect silence. Not the silence of absence, but the silence of presence—full, rich, and alive with possibility.</p>
                    </div>

                    <div class="memory-visual">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Sunrise meditation in the Himalayas" class="memory-image">
                        <div class="memory-caption">
                            Dawn meditation circle, where silence became the most eloquent teacher
                        </div>
                    </div>
                </div>

                <div class="testimonials">
                    <h3 class="testimonials-title">Voices from the Mountain</h3>
                    <div class="testimonial-grid">
                        <div class="testimonial">
                            <p class="testimonial-text">I went to Nepal carrying the weight of a failed marriage and a career that felt hollow. I returned with empty hands but a full heart, finally understanding that sometimes losing everything is the only way to find yourself.</p>
                            <div class="testimonial-author">— Elena Rodriguez, London</div>
                        </div>
                        <div class="testimonial">
                            <p class="testimonial-text">The mountains taught me that strength isn't about holding on—it's about knowing when to let go. I've never felt more powerful than in those moments of complete surrender.</p>
                            <div class="testimonial-author">— David Chen, San Francisco</div>
                        </div>
                        <div class="testimonial">
                            <p class="testimonial-text">In the silence between heartbeats, I found a peace I didn't know existed. This retreat didn't just change my meditation practice—it changed my entire relationship with being alive.</p>
                            <div class="testimonial-author">— Priya Sharma, Mumbai</div>
                        </div>
                    </div>
                </div>
            </article>

            <div class="memory-divider"></div>

            <!-- November 2024 Story -->
            <article class="memory-story" data-month="november-2024">
                <div class="memory-header">
                    <div class="memory-date">November 2024</div>
                    <h2 class="memory-title">Dancing with Shadows</h2>
                    <div class="memory-location">🌊 Coastal Cliffs, Big Sur, California</div>
                </div>

                <div class="memory-content">
                    <div class="memory-text">
                        <p class="opening">Where the Pacific meets the sky, eight brave souls learned that healing happens not by avoiding our shadows, but by dancing with them.</p>

                        <p>The November retreat was different—raw, honest, and unafraid of the darker corners of the human experience. <span class="highlight">Guided by the rhythm of crashing waves, participants explored the parts of themselves they'd spent years trying to hide.</span></p>

                        <p>Marcus, a veteran struggling with PTSD, found his voice again through movement therapy on the clifftops. "The ocean doesn't judge the storm," he said, tears mixing with sea spray. "It just holds it all."</p>

                        <p>Each morning began with yoga as the sun painted the sky in impossible colors. But the real magic happened in the afternoon shadow work sessions—guided journeys into the psyche where participants met their fears with compassion rather than resistance.</p>

                        <p><span class="highlight">By week's end, the group had transformed from strangers hiding behind masks to a chosen family celebrating their shared humanity.</span> They learned that wholeness doesn't mean perfection—it means embracing every part of who we are.</p>
                    </div>

                    <div class="memory-visual">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Sunset yoga on Big Sur cliffs" class="memory-image">
                        <div class="memory-caption">
                            Evening practice where shadows became teachers and vulnerability became strength
                        </div>
                    </div>
                </div>

                <div class="testimonials">
                    <h3 class="testimonials-title">Echoes from the Cliffs</h3>
                    <div class="testimonial-grid">
                        <div class="testimonial">
                            <p class="testimonial-text">I came here broken, thinking I needed to be fixed. Instead, I learned that my cracks are where the light gets in. This retreat taught me to love my whole story, not just the pretty parts.</p>
                            <div class="testimonial-author">— Marcus Williams, Denver</div>
                        </div>
                        <div class="testimonial">
                            <p class="testimonial-text">The shadow work was terrifying and beautiful. For the first time in my life, I met my anxiety with curiosity instead of fear. It's still there, but now it's a teacher, not a tyrant.</p>
                            <div class="testimonial-author">— Sarah Kim, Seattle</div>
                        </div>
                        <div class="testimonial">
                            <p class="testimonial-text">Watching the sunset from those cliffs, surrounded by people who truly saw me, I understood what belonging feels like. We didn't just heal individually—we healed together.</p>
                            <div class="testimonial-author">— Isabella Torres, Mexico City</div>
                        </div>
                    </div>
                </div>
            </article>

            <div class="memory-divider"></div>

            <!-- October 2024 Story -->
            <article class="memory-story" data-month="october-2024">
                <div class="memory-header">
                    <div class="memory-date">October 2024</div>
                    <h2 class="memory-title">The Forest's Secret Language</h2>
                    <div class="memory-location">🍂 Ancient Redwoods, Northern California</div>
                </div>

                <div class="memory-content">
                    <div class="memory-text">
                        <p class="opening">Among trees that have witnessed a thousand autumns, fifteen seekers discovered that nature speaks to those who remember how to listen.</p>

                        <p>The October forest bathing retreat was an invitation to remember our place in the web of life. <span class="highlight">Participants learned to move through the redwood cathedral not as visitors, but as part of the ancient conversation between earth and sky.</span></p>

                        <p>Dr. Amelia Foster, a burned-out physician, spent three hours sitting with a 2,000-year-old tree. "I realized I'd been treating symptoms instead of listening to what bodies were trying to tell me," she reflected. "The tree taught me that healing happens in its own time."</p>

                        <p>Daily practices included silent forest walks, tree meditation, and evening circles where participants shared the wisdom they'd received from their non-human teachers. <span class="highlight">The forest became both classroom and therapist, offering lessons in patience, resilience, and interconnection.</span></p>

                        <p>As autumn leaves spiraled down like golden prayers, the group understood that letting go isn't about loss—it's about making space for new growth.</p>
                    </div>

                    <div class="memory-visual">
                        <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Meditation among ancient redwoods" class="memory-image">
                        <div class="memory-caption">
                            Silent communion with ancient wisdom keepers in the cathedral of giants
                        </div>
                    </div>
                </div>

                <div class="testimonials">
                    <h3 class="testimonials-title">Whispers from the Woods</h3>
                    <div class="testimonial-grid">
                        <div class="testimonial">
                            <p class="testimonial-text">The redwoods taught me that true strength comes from deep roots and flexible branches. I returned to my medical practice with a completely new understanding of what it means to heal.</p>
                            <div class="testimonial-author">— Dr. Amelia Foster, Portland</div>
                        </div>
                        <div class="testimonial">
                            <p class="testimonial-text">I've never felt so small and so connected at the same time. Standing among those ancient giants, I understood that I'm part of something infinitely larger and more beautiful than my daily worries.</p>
                            <div class="testimonial-author">— James Liu, Vancouver</div>
                        </div>
                        <div class="testimonial">
                            <p class="testimonial-text">The forest showed me that growth happens slowly, quietly, and with deep patience. I stopped rushing my healing and started trusting the process. Everything changed.</p>
                            <div class="testimonial-author">— Luna Martinez, Austin</div>
                        </div>
                    </div>
                </div>
            </article>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <div class="logo"><a href="index.html">MindField</a></div>
                <p>Transform Your Life</p>
            </div>
            <div class="footer-links">
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="about.html">About Us</a>
                    <a href="#">Careers</a>
                    <a href="#">Press</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Blog</a>
                    <a href="#">Community</a>
                    <a href="curated-memories.html">Curated</a>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <a href="#">Help Center</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 MindField. All rights reserved.</p>
            <div class="social-icons">
                <a href="#"><i class="fab fa-facebook"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
        </div>
    </footer>

    <!-- Include necessary scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/error-handler.js"></script>
    <script src="js/accessibility.js"></script>
    <script src="js/performance-monitor.js"></script>

    <script>
        // Curated Memories specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            initMemoriesPage();
        });

        function initMemoriesPage() {
            try {
                // Animate memory stories on scroll
                const memoryStories = document.querySelectorAll('.memory-story');

                if (memoryStories.length === 0) {
                    console.warn('No memory stories found on page');
                    return;
                }

                // Check if IntersectionObserver is supported
                if (!window.IntersectionObserver) {
                    console.warn('IntersectionObserver not supported, falling back to simple animations');
                    // Fallback: just show all stories
                    memoryStories.forEach(story => {
                        story.classList.add('visible');
                        const testimonials = story.querySelectorAll('.testimonial');
                        testimonials.forEach(testimonial => {
                            testimonial.style.opacity = '1';
                            testimonial.style.transform = 'translateY(0)';
                        });
                    });
                    return;
                }

                const observerOptions = {
                    threshold: 0.2,
                    rootMargin: '0px 0px -100px 0px'
                };

                const storyObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        try {
                            if (entry.isIntersecting) {
                                entry.target.classList.add('visible');
                                // Add a slight delay for testimonials
                                setTimeout(() => {
                                    const testimonials = entry.target.querySelectorAll('.testimonial');
                                    testimonials.forEach((testimonial, index) => {
                                        if (testimonial) {
                                            setTimeout(() => {
                                                testimonial.style.opacity = '1';
                                                testimonial.style.transform = 'translateY(0)';
                                            }, index * 200);
                                        }
                                    });
                                }, 500);
                            }
                        } catch (error) {
                            console.error('Error in story observer:', error);
                        }
                    });
                }, observerOptions);

                memoryStories.forEach(story => {
                    try {
                        storyObserver.observe(story);

                        // Initialize testimonials as hidden
                        const testimonials = story.querySelectorAll('.testimonial');
                        testimonials.forEach(testimonial => {
                            if (testimonial) {
                                testimonial.style.opacity = '0';
                                testimonial.style.transform = 'translateY(20px)';
                                testimonial.style.transition = 'all 0.6s ease';
                            }
                        });
                    } catch (error) {
                        console.error('Error setting up story observer:', error);
                    }
                });

                // Add hover effects to memory images
                const memoryImages = document.querySelectorAll('.memory-image');
                memoryImages.forEach(image => {
                    try {
                        image.addEventListener('mouseenter', function() {
                            this.style.filter = 'brightness(1.1) saturate(1.2)';
                        });

                        image.addEventListener('mouseleave', function() {
                            this.style.filter = 'brightness(1) saturate(1)';
                        });
                    } catch (error) {
                        console.error('Error setting up image hover effects:', error);
                    }
                });

                // Smooth scroll for any internal links
                const internalLinks = document.querySelectorAll('a[href^="#"]');
                internalLinks.forEach(anchor => {
                    try {
                        anchor.addEventListener('click', function (e) {
                            e.preventDefault();
                            const href = this.getAttribute('href');
                            if (href && href !== '#') {
                                const target = document.querySelector(href);
                                if (target) {
                                    target.scrollIntoView({
                                        behavior: 'smooth',
                                        block: 'start'
                                    });
                                }
                            }
                        });
                    } catch (error) {
                        console.error('Error setting up smooth scroll:', error);
                    }
                });

                // Add parallax effect to floating elements with throttling
                let ticking = false;
                function updateParallax() {
                    try {
                        const scrolled = window.pageYOffset;
                        const parallax = document.querySelectorAll('.floating-element');

                        parallax.forEach((element, index) => {
                            if (element) {
                                const speed = 0.5 + (index * 0.1);
                                element.style.transform = `translateY(${scrolled * speed}px) rotate(${scrolled * 0.1}deg)`;
                            }
                        });
                        ticking = false;
                    } catch (error) {
                        console.error('Error in parallax update:', error);
                        ticking = false;
                    }
                }

                window.addEventListener('scroll', function() {
                    if (!ticking) {
                        requestAnimationFrame(updateParallax);
                        ticking = true;
                    }
                });

                console.log('🌟 Curated Memories page initialized with Soul Whispers™ style');

            } catch (error) {
                console.error('Error initializing memories page:', error);
                // Fallback: ensure basic functionality works
                const memoryStories = document.querySelectorAll('.memory-story');
                memoryStories.forEach(story => {
                    story.classList.add('visible');
                });
            }
        }

        // Admin mode toggle (for debugging)
        // To enable admin mode: add ?admin=true to URL or run enableAdminMode() in console
        function enableAdminMode() {
            localStorage.setItem('mindfield_admin', 'true');
            console.log('🔧 Admin mode enabled. Refresh page to see error notifications.');
        }

        function disableAdminMode() {
            localStorage.removeItem('mindfield_admin');
            console.log('👤 Admin mode disabled. Refresh page to hide error notifications.');
        }

        // Make admin functions globally available
        window.enableAdminMode = enableAdminMode;
        window.disableAdminMode = disableAdminMode;

        // Show admin status in console
        if (localStorage.getItem('mindfield_admin') === 'true' || new URLSearchParams(window.location.search).get('admin') === 'true') {
            console.log('🔧 Admin mode is ACTIVE - Error notifications will be shown');
            console.log('💡 To disable: run disableAdminMode() in console');
        } else {
            console.log('👤 User mode is ACTIVE - Error notifications are hidden');
            console.log('💡 To enable admin mode: run enableAdminMode() in console or add ?admin=true to URL');
        }
    </script>
</body>
</html>
