// Form validation and enhancement utilities for MindField

class FormValidator {
  constructor() {
    this.setupFormValidation();
  }

  // Setup form validation for all forms
  setupFormValidation() {
    document.addEventListener('DOMContentLoaded', () => {
      this.initializeFormValidation();
    });
  }

  // Initialize validation for all forms
  initializeFormValidation() {
    const forms = document.querySelectorAll('form, .auth-form');
    forms.forEach(form => {
      this.setupFormListeners(form);
    });
  }

  // Setup listeners for a specific form
  setupFormListeners(form) {
    const inputs = form.querySelectorAll('input, textarea, select');
    
    inputs.forEach(input => {
      // Real-time validation on blur
      input.addEventListener('blur', () => {
        this.validateField(input);
      });

      // Clear validation on focus
      input.addEventListener('focus', () => {
        this.clearFieldValidation(input);
      });

      // Real-time validation for password confirmation
      if (input.id === 'signup-confirm') {
        input.addEventListener('input', () => {
          this.validatePasswordConfirmation(input);
        });
      }

      // Real-time password strength indicator
      if (input.type === 'password' && input.id === 'signup-password') {
        input.addEventListener('input', () => {
          this.showPasswordStrength(input);
        });
      }
    });

    // Form submission validation
    const submitButton = form.querySelector('button[type="submit"], .btn-primary');
    if (submitButton) {
      submitButton.addEventListener('click', (e) => {
        if (!this.validateForm(form)) {
          e.preventDefault();
        }
      });
    }
  }

  // Validate a single field
  validateField(input) {
    const value = input.value.trim();
    const fieldName = input.getAttribute('data-label') || input.placeholder || input.name || 'Field';
    let isValid = true;
    let errorMessage = '';

    // Required field validation
    if (input.hasAttribute('required') && !value) {
      isValid = false;
      errorMessage = `${fieldName} is required.`;
    }
    // Email validation
    else if (input.type === 'email' && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid email address.';
      }
    }
    // Password validation
    else if (input.type === 'password' && value && input.id === 'signup-password') {
      const passwordValidation = this.validatePassword(value);
      if (!passwordValidation.isValid) {
        isValid = false;
        errorMessage = passwordValidation.message;
      }
    }
    // Password confirmation validation
    else if (input.id === 'signup-confirm' && value) {
      const password = document.getElementById('signup-password');
      if (password && value !== password.value) {
        isValid = false;
        errorMessage = 'Passwords do not match.';
      }
    }
    // Minimum length validation
    else if (input.hasAttribute('minlength') && value) {
      const minLength = parseInt(input.getAttribute('minlength'));
      if (value.length < minLength) {
        isValid = false;
        errorMessage = `${fieldName} must be at least ${minLength} characters.`;
      }
    }

    this.setFieldValidationState(input, isValid, errorMessage);
    return isValid;
  }

  // Validate entire form
  validateForm(form) {
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    let isFormValid = true;

    inputs.forEach(input => {
      if (!this.validateField(input)) {
        isFormValid = false;
      }
    });

    return isFormValid;
  }

  // Set validation state for a field
  setFieldValidationState(input, isValid, errorMessage = '') {
    const formGroup = input.closest('.form-group');
    if (!formGroup) return;

    // Remove existing validation classes and messages
    formGroup.classList.remove('error', 'success');
    const existingError = formGroup.querySelector('.form-error');
    if (existingError) {
      existingError.remove();
    }

    if (isValid && input.value.trim()) {
      formGroup.classList.add('success');
    } else if (!isValid) {
      formGroup.classList.add('error');
      
      // Add error message
      const errorElement = document.createElement('div');
      errorElement.className = 'form-error';
      errorElement.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${errorMessage}`;
      formGroup.appendChild(errorElement);
    }
  }

  // Clear field validation
  clearFieldValidation(input) {
    const formGroup = input.closest('.form-group');
    if (!formGroup) return;

    formGroup.classList.remove('error', 'success');
    const existingError = formGroup.querySelector('.form-error');
    if (existingError) {
      existingError.remove();
    }
  }

  // Validate password strength
  validatePassword(password) {
    const minLength = 8;
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);

    if (password.length < minLength) {
      return {
        isValid: false,
        message: `Password must be at least ${minLength} characters long.`,
        strength: 'weak'
      };
    }

    if (!hasNumber) {
      return {
        isValid: false,
        message: 'Password must contain at least one number.',
        strength: 'weak'
      };
    }

    if (!hasSpecial) {
      return {
        isValid: false,
        message: 'Password must contain at least one special character.',
        strength: 'weak'
      };
    }

    // Calculate strength
    let strength = 'weak';
    let score = 0;

    if (password.length >= 8) score++;
    if (hasNumber) score++;
    if (hasSpecial) score++;
    if (hasUpper) score++;
    if (hasLower) score++;

    if (score >= 4) strength = 'strong';
    else if (score >= 3) strength = 'medium';

    return {
      isValid: true,
      message: '',
      strength: strength,
      score: score
    };
  }

  // Show password strength indicator
  showPasswordStrength(input) {
    const password = input.value;
    const validation = this.validatePassword(password);
    
    // Remove existing strength indicator
    const existingIndicator = input.parentElement.querySelector('.password-strength');
    if (existingIndicator) {
      existingIndicator.remove();
    }

    if (password.length === 0) return;

    // Create strength indicator
    const strengthIndicator = document.createElement('div');
    strengthIndicator.className = 'password-strength';
    
    const strengthBar = document.createElement('div');
    strengthBar.className = `strength-bar strength-${validation.strength}`;
    
    const strengthText = document.createElement('span');
    strengthText.className = 'strength-text';
    strengthText.textContent = `Password strength: ${validation.strength.charAt(0).toUpperCase() + validation.strength.slice(1)}`;

    strengthIndicator.appendChild(strengthBar);
    strengthIndicator.appendChild(strengthText);
    
    input.parentElement.appendChild(strengthIndicator);
  }

  // Validate password confirmation
  validatePasswordConfirmation(confirmInput) {
    const passwordInput = document.getElementById('signup-password');
    if (!passwordInput) return;

    const password = passwordInput.value;
    const confirmation = confirmInput.value;

    if (confirmation && password !== confirmation) {
      this.setFieldValidationState(confirmInput, false, 'Passwords do not match.');
    } else if (confirmation && password === confirmation) {
      this.setFieldValidationState(confirmInput, true);
    }
  }

  // Enhanced form submission with loading states
  handleFormSubmission(form, submitHandler) {
    const submitButton = form.querySelector('button[type="submit"], .btn-primary');
    
    if (submitButton) {
      submitButton.addEventListener('click', async (e) => {
        e.preventDefault();
        
        if (!this.validateForm(form)) {
          return;
        }

        // Show loading state
        submitButton.classList.add('loading');
        submitButton.disabled = true;

        try {
          await submitHandler(form);
        } catch (error) {
          console.error('Form submission error:', error);
          if (window.errorHandler) {
            window.errorHandler.showNotification('An error occurred. Please try again.', 'error');
          }
        } finally {
          // Remove loading state
          submitButton.classList.remove('loading');
          submitButton.disabled = false;
        }
      });
    }
  }
}

// Initialize form validator
const formValidator = new FormValidator();

// Export for use in other modules
window.FormValidator = FormValidator;
window.formValidator = formValidator;
