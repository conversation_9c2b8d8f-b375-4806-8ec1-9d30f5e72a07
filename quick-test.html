<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindField Quick Test</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .quick-test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        
        .test-section h3 {
            color: #6c63ff;
            margin-top: 0;
        }
        
        .test-result {
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .test-success {
            background-color: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .test-error {
            background-color: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        
        .demo-form {
            display: grid;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .demo-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="quick-test-container">
        <h1>🧪 MindField Quick Test</h1>
        <p>This page tests the key improvements made to your MindField application.</p>
        
        <!-- Form Validation Test -->
        <div class="test-section">
            <h3>1. Form Validation Test</h3>
            <p>Test real-time form validation with error messages and success states.</p>
            
            <form class="demo-form">
                <div class="form-group">
                    <label for="test-email">Email Address</label>
                    <input type="email" id="test-email" required placeholder="Enter your email">
                </div>
                
                <div class="form-group">
                    <label for="test-password">Password</label>
                    <input type="password" id="test-password" required placeholder="Enter a strong password">
                </div>
                
                <div class="form-group">
                    <label for="test-confirm">Confirm Password</label>
                    <input type="password" id="test-confirm" required placeholder="Confirm your password">
                </div>
                
                <div class="form-group">
                    <label for="test-name">Full Name</label>
                    <input type="text" id="test-name" required placeholder="Enter your full name">
                </div>
            </form>
            
            <div class="test-result" id="validation-result">
                Try entering invalid data (like "invalid-email" or "123") and then valid data to see validation in action.
            </div>
        </div>
        
        <!-- Notification System Test -->
        <div class="test-section">
            <h3>2. Notification System Test</h3>
            <p>Test different types of notifications and their behavior.</p>
            
            <div class="demo-buttons">
                <button class="btn btn-primary" onclick="testSuccessNotification()">Success Notification</button>
                <button class="btn btn-secondary" onclick="testErrorNotification()">Error Notification</button>
                <button class="btn btn-outline" onclick="testWarningNotification()">Warning Notification</button>
                <button class="btn btn-text" onclick="testInfoNotification()">Info Notification</button>
            </div>
            
            <div class="test-result" id="notification-result">
                Click the buttons above to test different notification types. They should appear in the top-right corner.
            </div>
        </div>
        
        <!-- Loading States Test -->
        <div class="test-section">
            <h3>3. Loading States Test</h3>
            <p>Test loading indicators and button states during async operations.</p>
            
            <div class="demo-buttons">
                <button class="btn btn-primary" id="loading-test-btn" onclick="testLoadingState()">Test Loading State</button>
                <button class="btn btn-outline" onclick="testFormLoading()">Test Form Loading</button>
            </div>
            
            <div id="loading-demo-form" style="margin-top: 1rem; padding: 1rem; border: 1px solid #e0e0e0; border-radius: 4px;">
                <p>Form content for loading overlay test</p>
            </div>
            
            <div class="test-result" id="loading-result">
                Click buttons above to see loading states in action.
            </div>
        </div>
        
        <!-- Accessibility Test -->
        <div class="test-section">
            <h3>4. Accessibility Test</h3>
            <p>Test keyboard navigation and accessibility features.</p>
            
            <div class="demo-buttons">
                <button class="btn btn-primary" onclick="testKeyboardNavigation()">Test Keyboard Focus</button>
                <button class="btn btn-outline" onclick="testScreenReaderAnnouncement()">Test Screen Reader</button>
                <button class="btn btn-secondary" onclick="testSkipLinks()">Test Skip Links</button>
            </div>
            
            <div class="test-result" id="accessibility-result">
                Use Tab key to navigate, Enter/Space to activate buttons. Test with screen reader if available.
            </div>
        </div>
        
        <!-- Performance Test -->
        <div class="test-section">
            <h3>5. Performance Test</h3>
            <p>Test performance optimizations and monitoring.</p>
            
            <div class="demo-buttons">
                <button class="btn btn-primary" onclick="testDebouncing()">Test Debouncing</button>
                <button class="btn btn-outline" onclick="testCaching()">Test Caching</button>
                <button class="btn btn-secondary" onclick="showPerformanceMetrics()">Show Metrics</button>
            </div>
            
            <input type="search" id="debounce-test" placeholder="Type here to test debouncing..." style="margin-top: 1rem; width: 100%;">
            
            <div class="test-result" id="performance-result">
                Type rapidly in the search box above to see debouncing in action.
            </div>
        </div>
        
        <!-- Integration Test -->
        <div class="test-section">
            <h3>6. Integration Test</h3>
            <p>Test how all systems work together.</p>
            
            <div class="demo-buttons">
                <button class="btn btn-primary" onclick="runIntegrationTest()">Run Integration Test</button>
                <button class="btn btn-outline" onclick="resetTests()">Reset All Tests</button>
            </div>
            
            <div class="test-result" id="integration-result">
                Click "Run Integration Test" to see all systems working together.
            </div>
        </div>
    </div>

    <!-- Include necessary scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/error-handler.js"></script>
    <script src="js/form-validator.js"></script>
    <script src="js/accessibility.js"></script>
    <script src="js/performance-monitor.js"></script>
    <script src="js/performance.js"></script>

    <script>
        // Test functions
        function testSuccessNotification() {
            if (window.errorHandler) {
                window.errorHandler.showNotification('✅ Success! This is a success notification.', 'success');
                updateResult('notification-result', 'Success notification displayed!', 'success');
            } else {
                updateResult('notification-result', 'Error handler not available', 'error');
            }
        }

        function testErrorNotification() {
            if (window.errorHandler) {
                window.errorHandler.showNotification('❌ Error! This is an error notification.', 'error');
                updateResult('notification-result', 'Error notification displayed!', 'success');
            } else {
                updateResult('notification-result', 'Error handler not available', 'error');
            }
        }

        function testWarningNotification() {
            if (window.errorHandler) {
                window.errorHandler.showNotification('⚠️ Warning! This is a warning notification.', 'warning');
                updateResult('notification-result', 'Warning notification displayed!', 'success');
            } else {
                updateResult('notification-result', 'Error handler not available', 'error');
            }
        }

        function testInfoNotification() {
            if (window.errorHandler) {
                window.errorHandler.showNotification('ℹ️ Info! This is an info notification.', 'info');
                updateResult('notification-result', 'Info notification displayed!', 'success');
            } else {
                updateResult('notification-result', 'Error handler not available', 'error');
            }
        }

        async function testLoadingState() {
            const button = document.getElementById('loading-test-btn');
            
            // Show loading state
            button.classList.add('loading');
            button.disabled = true;
            
            updateResult('loading-result', 'Button is now in loading state...', 'success');
            
            // Simulate async operation
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Remove loading state
            button.classList.remove('loading');
            button.disabled = false;
            
            updateResult('loading-result', 'Loading state test completed!', 'success');
        }

        function testFormLoading() {
            const form = document.getElementById('loading-demo-form');
            
            if (window.errorHandler) {
                window.errorHandler.showLoading(form, 'Loading form data...');
                updateResult('loading-result', 'Form loading overlay displayed!', 'success');
                
                setTimeout(() => {
                    window.errorHandler.hideLoading(form);
                    updateResult('loading-result', 'Form loading test completed!', 'success');
                }, 2000);
            } else {
                updateResult('loading-result', 'Error handler not available', 'error');
            }
        }

        function testKeyboardNavigation() {
            document.body.classList.add('keyboard-navigation');
            updateResult('accessibility-result', 'Keyboard navigation mode enabled. Use Tab to navigate and see focus indicators.', 'success');
        }

        function testScreenReaderAnnouncement() {
            if (window.accessibilityManager) {
                window.accessibilityManager.announceToScreenReader('This is a test announcement for screen readers.');
                updateResult('accessibility-result', 'Screen reader announcement sent! (Check with screen reader)', 'success');
            } else {
                updateResult('accessibility-result', 'Accessibility manager not available', 'error');
            }
        }

        function testSkipLinks() {
            const skipLink = document.querySelector('.skip-link');
            if (skipLink) {
                skipLink.focus();
                updateResult('accessibility-result', 'Skip link focused! Press Tab to see it appear.', 'success');
            } else {
                updateResult('accessibility-result', 'Skip link not found', 'error');
            }
        }

        function testDebouncing() {
            const searchInput = document.getElementById('debounce-test');
            let searchCount = 0;
            
            if (window.debounce) {
                const debouncedSearch = window.debounce(() => {
                    searchCount++;
                    updateResult('performance-result', `Debounced search executed ${searchCount} times. Type rapidly to see debouncing effect.`, 'success');
                }, 300);
                
                searchInput.addEventListener('input', debouncedSearch);
                updateResult('performance-result', 'Debouncing test setup complete. Type in the search box above.', 'success');
            } else {
                updateResult('performance-result', 'Debounce function not available', 'error');
            }
        }

        function testCaching() {
            if (window.dataCache) {
                // Test cache operations
                window.dataCache.setItem('test-key', { message: 'Test cache data', timestamp: Date.now() });
                const cachedData = window.dataCache.getItem('test-key');
                
                if (cachedData && cachedData.message === 'Test cache data') {
                    updateResult('performance-result', 'Caching system working correctly!', 'success');
                } else {
                    updateResult('performance-result', 'Caching test failed', 'error');
                }
            } else {
                updateResult('performance-result', 'Data cache not available', 'error');
            }
        }

        function showPerformanceMetrics() {
            if (window.performanceMonitor) {
                const report = window.performanceMonitor.generateReport();
                const metrics = `
                    Session Duration: ${(report.sessionDuration / 1000).toFixed(2)}s
                    Memory Usage: ${report.memory ? (report.memory.used / 1024 / 1024).toFixed(2) + 'MB' : 'N/A'}
                    Interactions: ${report.interactions.total}
                    Errors: ${report.errors}
                `;
                updateResult('performance-result', `Performance Metrics:${metrics}`, 'success');
            } else {
                updateResult('performance-result', 'Performance monitor not available', 'error');
            }
        }

        async function runIntegrationTest() {
            updateResult('integration-result', 'Running integration test...', 'success');
            
            // Test 1: Form validation
            const emailInput = document.getElementById('test-email');
            emailInput.value = '<EMAIL>';
            emailInput.dispatchEvent(new Event('blur'));
            
            await new Promise(resolve => setTimeout(resolve, 200));
            
            // Test 2: Show notification
            if (window.errorHandler) {
                window.errorHandler.showNotification('Integration test in progress...', 'info');
            }
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Test 3: Test loading state
            const integrationBtn = event.target;
            integrationBtn.classList.add('loading');
            integrationBtn.disabled = true;
            
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            integrationBtn.classList.remove('loading');
            integrationBtn.disabled = false;
            
            // Test 4: Final success
            if (window.errorHandler) {
                window.errorHandler.showNotification('🎉 Integration test completed successfully!', 'success');
            }
            
            updateResult('integration-result', '✅ Integration test passed! All systems working together.', 'success');
        }

        function resetTests() {
            // Clear all test results
            const results = document.querySelectorAll('.test-result');
            results.forEach(result => {
                result.className = 'test-result';
                result.textContent = 'Test reset. Ready for testing.';
            });
            
            // Clear form inputs
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.value = '';
                const formGroup = input.closest('.form-group');
                if (formGroup) {
                    formGroup.classList.remove('error', 'success');
                    const errorMsg = formGroup.querySelector('.form-error');
                    if (errorMsg) errorMsg.remove();
                }
            });
            
            // Clear notifications
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(n => n.remove());
            
            if (window.errorHandler) {
                window.errorHandler.showNotification('All tests reset successfully!', 'info');
            }
        }

        function updateResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-result test-${type}`;
        }

        // Initialize form validation on page load
        document.addEventListener('DOMContentLoaded', () => {
            // Setup form validation for demo form
            const inputs = document.querySelectorAll('#test-email, #test-password, #test-confirm, #test-name');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    if (window.formValidator) {
                        window.formValidator.validateField(input);
                    }
                });
                
                input.addEventListener('focus', () => {
                    if (window.formValidator) {
                        window.formValidator.clearFieldValidation(input);
                    }
                });
            });
            
            // Setup password confirmation validation
            const confirmInput = document.getElementById('test-confirm');
            if (confirmInput) {
                confirmInput.addEventListener('input', () => {
                    if (window.formValidator) {
                        window.formValidator.validatePasswordConfirmation(confirmInput);
                    }
                });
            }
            
            // Setup password strength indicator
            const passwordInput = document.getElementById('test-password');
            if (passwordInput) {
                passwordInput.addEventListener('input', () => {
                    if (window.formValidator) {
                        window.formValidator.showPasswordStrength(passwordInput);
                    }
                });
            }
            
            console.log('🧪 Quick test page initialized!');
            console.log('Available test functions:', {
                errorHandler: !!window.errorHandler,
                formValidator: !!window.formValidator,
                accessibilityManager: !!window.accessibilityManager,
                performanceMonitor: !!window.performanceMonitor,
                debounce: !!window.debounce,
                dataCache: !!window.dataCache
            });
        });
    </script>
</body>
</html>
