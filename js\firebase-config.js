// Firebase configuration for MindField
// Replace these values with your actual Firebase project configuration
const firebaseConfig = {
  apiKey: "AIzaSyDXQ9paNBmJ4qFwCFIdTL5BZzfU1BpBwXs",
  authDomain: "hhjk-c26b6.firebaseapp.com",
  projectId: "hhjk-c26b6",
  storageBucket: "hhjk-c26b6.firebasestorage.app",
  messagingSenderId: "27563337599",
  appId: "1:27563337599:web:ccc44e6f46f8ee2aec5c4a",
  measurementId: "G-GK1GGZR1E9"
};

// Environment configuration
const ENV_CONFIG = {
  isDevelopment: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1',
  isProduction: window.location.hostname.includes('firebaseapp.com') || window.location.hostname.includes('web.app')
};

// Initialize Firebase and services with error handling
let auth, db, storage;

try {
  // Check if Firebase is available
  if (typeof firebase !== 'undefined') {
    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);

    // Initialize services
    auth = firebase.auth();
    db = firebase.firestore();
    storage = firebase.storage();

    console.log('Firebase initialized successfully');
  } else {
    console.log('Firebase SDK not loaded - continuing without authentication');
  }
} catch (error) {
  console.error('Error initializing Firebase:', error);
}

// Export for use in other files
const firebaseServices = {
  auth,
  db,
  storage,
  firebase
};
