// Accessibility tests for MindField

testFramework.test('Accessibility - ARIA Labels and Roles', async () => {
  // Test navigation role
  const navbar = document.querySelector('.navbar');
  if (navbar) {
    testFramework.assertEqual(navbar.getAttribute('role'), 'navigation', 'Navbar should have navigation role');
    testFramework.assertNotNull(navbar.getAttribute('aria-label'), 'Navbar should have aria-label');
  }
  
  // Test main content role
  const main = document.querySelector('main');
  if (main) {
    testFramework.assertEqual(main.getAttribute('role'), 'main', 'Main element should have main role');
  }
  
  // Test footer role
  const footer = document.querySelector('footer');
  if (footer) {
    testFramework.assertEqual(footer.getAttribute('role'), 'contentinfo', 'Footer should have contentinfo role');
  }
});

testFramework.test('Accessibility - Skip Links', async () => {
  const skipLink = document.querySelector('.skip-link');
  testFramework.assertNotNull(skipLink, 'Skip link should exist');
  
  if (skipLink) {
    testFramework.assertEqual(skipLink.getAttribute('href'), '#main-content', 'Skip link should point to main content');
    testFramework.assertNotNull(skipLink.getAttribute('aria-label'), 'Skip link should have aria-label');
  }
  
  // Test main content has correct ID
  const mainContent = document.querySelector('#main-content');
  testFramework.assertNotNull(mainContent, 'Main content should have correct ID for skip link');
});

testFramework.test('Accessibility - Keyboard Navigation', async () => {
  // Create test button
  const testButton = testFramework.createElement('button', { 
    class: 'test-keyboard-btn',
    tabindex: '0'
  }, 'Test Button');
  document.body.appendChild(testButton);
  
  // Test keyboard activation
  let clicked = false;
  testButton.addEventListener('click', () => { clicked = true; });
  
  // Simulate Enter key
  testFramework.simulateKeyPress(testButton, 'Enter');
  await testFramework.wait(100);
  
  // Note: This test might not work in all environments due to security restrictions
  // In a real test environment, you would verify keyboard navigation manually
  
  // Test focus indicators
  testButton.focus();
  const computedStyle = window.getComputedStyle(testButton, ':focus');
  
  // Cleanup
  document.body.removeChild(testButton);
  
  testFramework.assertTrue(true, 'Keyboard navigation test completed (manual verification required)');
});

testFramework.test('Accessibility - Form Labels and Descriptions', async () => {
  // Create test form
  const form = testFramework.createElement('form', {}, `
    <div class="form-group">
      <label for="test-accessibility-input">Test Input</label>
      <input type="text" id="test-accessibility-input" required>
    </div>
  `);
  document.body.appendChild(form);
  
  const input = form.querySelector('#test-accessibility-input');
  const label = form.querySelector('label');
  
  // Test label association
  testFramework.assertEqual(label.getAttribute('for'), input.id, 'Label should be associated with input');
  
  // Test error message accessibility
  testFramework.simulateInput(input, '');
  input.dispatchEvent(new Event('blur'));
  
  await testFramework.wait(200);
  
  const errorElement = form.querySelector('.form-error');
  if (errorElement) {
    testFramework.assertNotNull(input.getAttribute('aria-describedby'), 'Input should have aria-describedby for error');
    testFramework.assertEqual(input.getAttribute('aria-invalid'), 'true', 'Input should have aria-invalid when error exists');
  }
  
  // Cleanup
  document.body.removeChild(form);
});

testFramework.test('Accessibility - Focus Management', async () => {
  // Test focus trap would require modal testing
  // For now, test basic focus management
  
  // Create focusable elements
  const container = testFramework.createElement('div', {}, `
    <button id="btn1">Button 1</button>
    <button id="btn2">Button 2</button>
    <input id="input1" type="text">
  `);
  document.body.appendChild(container);
  
  const btn1 = container.querySelector('#btn1');
  const btn2 = container.querySelector('#btn2');
  const input1 = container.querySelector('#input1');
  
  // Test programmatic focus
  btn1.focus();
  testFramework.assertEqual(document.activeElement, btn1, 'Focus should move to button 1');
  
  btn2.focus();
  testFramework.assertEqual(document.activeElement, btn2, 'Focus should move to button 2');
  
  input1.focus();
  testFramework.assertEqual(document.activeElement, input1, 'Focus should move to input');
  
  // Cleanup
  document.body.removeChild(container);
});

testFramework.test('Accessibility - High Contrast Support', async () => {
  // Test high contrast media query support
  const supportsHighContrast = window.matchMedia('(prefers-contrast: high)');
  testFramework.assertNotNull(supportsHighContrast, 'Browser should support high contrast media query');
  
  // Test that CSS variables are defined for high contrast
  const rootStyles = getComputedStyle(document.documentElement);
  const primaryColor = rootStyles.getPropertyValue('--primary-color');
  testFramework.assertNotNull(primaryColor, 'Primary color CSS variable should be defined');
  
  testFramework.assertTrue(true, 'High contrast support test completed');
});

testFramework.test('Accessibility - Reduced Motion Support', async () => {
  // Test reduced motion media query support
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
  testFramework.assertNotNull(prefersReducedMotion, 'Browser should support reduced motion media query');
  
  // Test that reduced motion class can be applied
  document.body.classList.add('reduced-motion');
  testFramework.assertTrue(document.body.classList.contains('reduced-motion'), 'Reduced motion class should be applicable');
  
  // Cleanup
  document.body.classList.remove('reduced-motion');
  
  testFramework.assertTrue(true, 'Reduced motion support test completed');
});

testFramework.test('Accessibility - Screen Reader Announcements', async () => {
  if (window.accessibilityManager) {
    // Test announcement function
    window.accessibilityManager.announceToScreenReader('Test announcement');
    
    await testFramework.wait(100);
    
    const announcement = document.querySelector('[aria-live]');
    testFramework.assertNotNull(announcement, 'Announcement element should be created');
    
    if (announcement) {
      testFramework.assertEqual(announcement.textContent, 'Test announcement', 'Announcement should contain correct text');
      testFramework.assertTrue(announcement.classList.contains('sr-only'), 'Announcement should be screen reader only');
    }
    
    // Wait for cleanup
    await testFramework.wait(1100);
    
    const cleanedAnnouncement = document.querySelector('[aria-live]');
    testFramework.assert(!cleanedAnnouncement, 'Announcement should be cleaned up after timeout');
  } else {
    throw new Error('Accessibility manager not available');
  }
});

testFramework.test('Accessibility - Interactive Element Sizes', async () => {
  // Test that interactive elements meet minimum size requirements (44px)
  const buttons = document.querySelectorAll('button, .btn');
  
  buttons.forEach((button, index) => {
    const rect = button.getBoundingClientRect();
    const minSize = 44;
    
    // Allow some tolerance for styling
    const tolerance = 5;
    
    if (rect.height > 0 && rect.width > 0) { // Only test visible elements
      testFramework.assertTrue(
        rect.height >= (minSize - tolerance) || rect.width >= (minSize - tolerance),
        `Button ${index} should meet minimum size requirements (44px)`
      );
    }
  });
  
  testFramework.assertTrue(true, 'Interactive element size test completed');
});
