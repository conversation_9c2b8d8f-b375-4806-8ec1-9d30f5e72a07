/* Animations CSS */

/* Base Animation Properties */
.animate-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.animate-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Staggered Animation Delays */
.hero-content .animate-in:nth-child(1) {
    transition-delay: 0.1s;
}

.hero-content .animate-in:nth-child(2) {
    transition-delay: 0.3s;
}

.hero-content .animate-in:nth-child(3) {
    transition-delay: 0.5s;
}

.hero-image.animate-in {
    transition-delay: 0.3s;
}

.feature .animate-in:nth-child(1) {
    transition-delay: 0.1s;
}

.feature .animate-in:nth-child(2) {
    transition-delay: 0.3s;
}

.feature .animate-in:nth-child(3) {
    transition-delay: 0.5s;
}

.cta-section .animate-in:nth-child(1) {
    transition-delay: 0.1s;
}

.cta-section .animate-in:nth-child(2) {
    transition-delay: 0.3s;
}

.groups-container .group-card:nth-child(1) {
    transition-delay: 0.1s;
}

.groups-container .group-card:nth-child(2) {
    transition-delay: 0.3s;
}

.groups-container .group-card:nth-child(3) {
    transition-delay: 0.5s;
}

.dashboard-stats .stat-card:nth-child(1) {
    transition-delay: 0.1s;
}

.dashboard-stats .stat-card:nth-child(2) {
    transition-delay: 0.2s;
}

.dashboard-stats .stat-card:nth-child(3) {
    transition-delay: 0.3s;
}

.dashboard-stats .stat-card:nth-child(4) {
    transition-delay: 0.4s;
}

.dashboard-sections .dashboard-section:nth-child(1) {
    transition-delay: 0.3s;
}

.dashboard-sections .dashboard-section:nth-child(2) {
    transition-delay: 0.5s;
}

.group-cards .group-membership-card:nth-child(1) {
    transition-delay: 0.1s;
}

.group-cards .group-membership-card:nth-child(2) {
    transition-delay: 0.3s;
}

.group-cards .group-membership-card:nth-child(3) {
    transition-delay: 0.5s;
}

/* Button Hover Animations */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.3);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.btn:hover::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

/* Card Hover Animations */
.group-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.group-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Navigation Hover Animation */
.nav-links a {
    position: relative;
}

.nav-links a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

/* Logo Animation - Removed */
.logo {
    display: inline-block;
}

/* Hamburger Menu Animation */
.hamburger.active span:nth-child(1) {
    transform: translateY(7px) rotate(45deg);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: translateY(-7px) rotate(-45deg);
}

/* Profile Tab Transition */
.profile-tab {
    transition: opacity 0.3s ease;
}

/* Map Marker Pulse Animation */
@keyframes markerPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.marker-pulse {
    animation: markerPulse 1.5s infinite ease-in-out;
}

/* Scroll Indicator Animation */
@keyframes scrollIndicator {
    0% {
        transform: translateY(0);
        opacity: 0.8;
    }
    50% {
        transform: translateY(10px);
        opacity: 0.4;
    }
    100% {
        transform: translateY(0);
        opacity: 0.8;
    }
}

.scroll-indicator {
    animation: scrollIndicator 2s infinite ease-in-out;
}

/* Fade In Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Slide In Animation */
@keyframes slideInRight {
    from {
        transform: translateX(50px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-in-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-50px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-in-out;
}

/* Rotate Animation */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Rotate animation removed */

/* Shake Animation */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

.shake {
    animation: shake 0.8s ease-in-out;
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

.bounce {
    animation: bounce 2s ease infinite;
}

/* Pulse Animation */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s ease infinite;
}

/* Floating Animation */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.float {
    animation: float 3s ease-in-out infinite;
}

/* Glow Animation */
@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(106, 90, 205, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(106, 90, 205, 0.8);
    }
    100% {
        box-shadow: 0 0 5px rgba(106, 90, 205, 0.5);
    }
}

.glow {
    animation: glow 2s ease-in-out infinite;
}

/* Typing Animation */
@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

.typing {
    overflow: hidden;
    white-space: nowrap;
    animation: typing 3.5s steps(40, end);
}

/* Fade In Up Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

/* Fade In Down Animation */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-down {
    animation: fadeInDown 0.5s ease-out;
}
