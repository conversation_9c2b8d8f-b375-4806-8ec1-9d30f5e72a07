<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindField - Our Story</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* About page specific styles */
        .about-hero {
            background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
            color: white;
            padding: 100px 0 50px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .about-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .about-hero p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto 2rem;
            line-height: 1.6;
        }

        .story-container {
            max-width: var(--container-width);
            margin: 0 auto;
            padding: 50px 20px;
        }

        .story-section {
            margin-bottom: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .story-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .story-header h2 {
            color: var(--primary-color);
            font-size: 2.2rem;
            margin-bottom: 1rem;
        }

        .story-header p {
            color: var(--text-light);
            font-size: 1.1rem;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .story-content {
            display: flex;
            flex-wrap: wrap;
            gap: 40px;
            align-items: center;
            justify-content: center;
        }

        .story-text {
            flex: 1;
            min-width: 300px;
        }

        .story-text p {
            margin-bottom: 1.5rem;
            line-height: 1.8;
            color: var(--text-color);
        }

        .story-image {
            flex: 1;
            min-width: 300px;
            max-width: 500px;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }

        .story-image img {
            width: 100%;
            height: auto;
            display: block;
            transition: transform 0.5s ease;
        }

        .story-image:hover img {
            transform: scale(1.03);
        }

        .story-timeline {
            width: 100%;
            max-width: 800px;
            margin: 60px auto;
            position: relative;
        }

        .timeline-line {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 50%;
            width: 4px;
            background-color: var(--primary-light);
            transform: translateX(-50%);
        }

        .timeline-item {
            display: flex;
            justify-content: flex-end;
            padding-right: 30px;
            position: relative;
            margin-bottom: 50px;
            width: 50%;
        }

        .timeline-item:nth-child(even) {
            align-self: flex-end;
            justify-content: flex-start;
            padding-right: 0;
            padding-left: 30px;
            margin-left: 50%;
        }

        .timeline-content {
            background-color: white;
            padding: 20px;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            width: 100%;
            max-width: 350px;
        }

        .timeline-content h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .timeline-content p {
            margin-bottom: 0;
            line-height: 1.6;
        }

        .timeline-dot {
            width: 20px;
            height: 20px;
            background-color: var(--primary-color);
            border-radius: 50%;
            position: absolute;
            top: 15px;
            left: calc(50% - 10px);
            z-index: 2;
        }

        .timeline-item:nth-child(even) .timeline-dot {
            left: calc(0% - 10px);
        }

        .values-section {
            background-color: var(--background-light);
            padding: 80px 0;
            text-align: center;
        }

        .values-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            max-width: var(--container-width);
            margin: 40px auto 0;
            padding: 0 20px;
        }

        .value-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 30px;
            flex: 1;
            min-width: 250px;
            max-width: 350px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .value-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .value-icon {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .value-card h3 {
            margin-bottom: 15px;
            color: var(--primary-dark);
        }

        .value-card p {
            color: var(--text-light);
            line-height: 1.6;
        }

        .team-section {
            padding: 80px 0;
            text-align: center;
        }

        .team-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            max-width: var(--container-width);
            margin: 40px auto 0;
            padding: 0 20px;
        }

        .team-member {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
            flex: 1;
            min-width: 250px;
            max-width: 300px;
            transition: transform 0.3s ease;
        }

        .team-member:hover {
            transform: translateY(-5px);
        }

        .team-photo {
            width: 100%;
            height: 250px;
            overflow: hidden;
        }

        .team-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .team-member:hover .team-photo img {
            transform: scale(1.05);
        }

        .team-info {
            padding: 20px;
        }

        .team-info h3 {
            margin-bottom: 5px;
            color: var(--primary-dark);
        }

        .team-info p {
            color: var(--text-light);
            margin-bottom: 15px;
        }

        .team-social {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .team-social a {
            color: var(--primary-color);
            font-size: 1.2rem;
            transition: color 0.3s ease;
        }

        .team-social a:hover {
            color: var(--primary-dark);
        }

        .journey-animation {
            position: relative;
            width: 100%;
            height: 300px;
            margin: 60px 0;
            overflow: hidden;
        }

        .journey-path {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 4px;
            background-color: var(--primary-light);
            transform: translateY(-50%);
        }

        .journey-milestone {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 30px;
            height: 30px;
            background-color: white;
            border: 4px solid var(--primary-color);
            border-radius: 50%;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-weight: bold;
            opacity: 0;
        }

        .journey-milestone.active {
            opacity: 1;
        }

        .journey-label {
            position: absolute;
            width: 150px;
            text-align: center;
            color: var(--text-color);
            font-weight: 500;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .journey-label.top {
            bottom: calc(50% + 40px);
        }

        .journey-label.bottom {
            top: calc(50% + 40px);
        }

        .journey-milestone.active + .journey-label {
            opacity: 1;
        }

        .journey-icon {
            position: absolute;
            top: 50%;
            left: -50px;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            z-index: 3;
            animation: journeyMove 15s linear infinite;
        }

        @keyframes journeyMove {
            0% {
                left: -50px;
            }
            100% {
                left: calc(100% + 50px);
            }
        }

        .cta-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }

        .cta-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .cta-content h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .cta-content p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .cta-buttons {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .story-content {
                flex-direction: column;
            }

            .story-image {
                order: -1;
                margin-bottom: 30px;
            }

            .timeline-line {
                left: 20px;
            }

            .timeline-item {
                width: 100%;
                padding-left: 50px;
                padding-right: 0;
            }

            .timeline-item:nth-child(even) {
                margin-left: 0;
                padding-left: 50px;
            }

            .timeline-dot {
                left: 20px;
            }

            .timeline-item:nth-child(even) .timeline-dot {
                left: 20px;
            }

            .journey-animation {
                height: 400px;
            }

            .journey-label {
                width: 120px;
            }
        }

        /* Animation for the story sections */
        .fade-in-section {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }

        .fade-in-section.is-visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Dark Mode Support for About Page */
        @media (prefers-color-scheme: dark) {
            .about-hero {
                background: linear-gradient(135deg, #8a84ff 0%, #6c63ff 100%);
            }

            .about-hero h1,
            .about-hero p {
                color: #ffffff;
            }

            .story-container {
                background-color: #1a202c;
            }

            .story-section {
                background-color: #2d3748;
                color: #e2e8f0;
            }

            .story-header h2 {
                color: #8a84ff;
            }

            .story-header p {
                color: #a0aec0;
            }

            .story-text p {
                color: #e2e8f0;
            }

            .journey-path {
                background: linear-gradient(to right, #8a84ff, #a29dff);
            }

            .journey-milestone {
                background-color: #2d3748;
                border-color: #8a84ff;
            }

            .journey-milestone.active {
                background-color: #8a84ff;
                box-shadow: 0 0 20px rgba(138, 132, 255, 0.5);
            }

            .journey-label {
                color: #e2e8f0;
                background-color: #2d3748;
                border-color: #4a5568;
            }

            .journey-icon {
                background: linear-gradient(135deg, #8a84ff 0%, #a29dff 100%);
                color: #ffffff;
            }

            .timeline-line {
                background-color: #4a5568;
            }

            .timeline-item .timeline-content {
                background-color: #2d3748;
                color: #e2e8f0;
                border-color: #4a5568;
            }

            .timeline-item .timeline-content h3 {
                color: #8a84ff;
            }

            .timeline-dot {
                background-color: #8a84ff;
                border-color: #2d3748;
            }

            .values-section {
                background-color: #1a202c;
            }

            .values-section h2 {
                color: #e2e8f0;
            }

            .values-section > p {
                color: #a0aec0;
            }

            .value-card {
                background-color: #2d3748;
                color: #e2e8f0;
                border-color: #4a5568;
            }

            .value-card:hover {
                background-color: #4a5568;
                box-shadow: 0 10px 30px rgba(138, 132, 255, 0.2);
            }

            .value-icon {
                background: linear-gradient(135deg, #8a84ff 0%, #a29dff 100%);
                color: #ffffff;
            }

            .value-card h3 {
                color: #e2e8f0;
            }

            .value-card p {
                color: #a0aec0;
            }

            .team-section {
                background-color: #2d3748;
            }

            .team-section h2 {
                color: #e2e8f0;
            }

            .team-section > .container > p {
                color: #a0aec0;
            }

            .team-member {
                background-color: #1a202c;
                border-color: #4a5568;
            }

            .team-member:hover {
                background-color: #2d3748;
                box-shadow: 0 10px 30px rgba(138, 132, 255, 0.2);
            }

            .team-info h3 {
                color: #e2e8f0;
            }

            .team-info p {
                color: #8a84ff;
            }

            .team-social a {
                color: #a0aec0;
                background-color: #4a5568;
            }

            .team-social a:hover {
                color: #ffffff;
                background-color: #8a84ff;
            }
        }
    </style>
</head>
<body class="about-page">
    <header>
        <nav class="navbar">
            <div class="logo"><a href="index.html">MindField</a></div>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="about.html" class="active">About</a>
                <a href="guides.html">Guides</a>
                <a href="groups.html">Groups</a>
                <a href="browse.html">Browse</a>
                <a href="index.html#contact">Contact</a>
            </div>
            <div class="profile-icon">
                <a href="login.html"><i class="fas fa-user-circle"></i></a>
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <main>
        <section class="about-hero">
            <div class="container">
                <h1 class="animate-in">Our Story</h1>
                <p class="animate-in">How a personal tragedy transformed into a global movement for mindfulness and connection.</p>
            </div>
        </section>

        <div class="story-container">
            <section class="story-section fade-in-section">
                <div class="story-header">
                    <h2>The Beginning</h2>
                    <p>Every journey begins with a single step, but sometimes that step is taken in the darkest of moments.</p>
                </div>
                <div class="story-content">
                    <div class="story-text">
                        <p>MindField began in 2018 when our founder, Sarah Chen, experienced a profound personal loss. After the sudden death of her brother to suicide, Sarah found herself spiraling into depression and anxiety. Traditional therapy helped, but it was a 10-day silent meditation retreat in Bali that truly began her healing journey.</p>
                        <p>"I remember sitting there on the final day of the retreat, watching the sunrise over Mount Agung, when I felt something shift inside me," Sarah recalls. "It wasn't that my grief had disappeared—it was that I'd found a way to hold it differently, with compassion rather than resistance."</p>
                        <p>That transformative experience planted the seed for what would become MindField: a platform connecting people seeking inner peace with transformative retreat experiences and the guides and spaces that make them possible.</p>
                    </div>
                    <div class="story-image">
                        <img src="https://images.unsplash.com/photo-1604881991720-f91add269bed?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Meditation retreat in Bali">
                    </div>
                </div>
            </section>

            <section class="story-section fade-in-section">
                <div class="story-header">
                    <h2>The Vision Takes Shape</h2>
                    <p>From personal healing to a mission of global connection.</p>
                </div>
                <div class="story-content">
                    <div class="story-image">
                        <img src="https://images.unsplash.com/photo-1536623975707-c4b3b2af565d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Team brainstorming session">
                    </div>
                    <div class="story-text">
                        <p>Returning home, Sarah couldn't shake the feeling that something important was missing in the wellness space. While there were plenty of retreat centers and independent teachers, there was no central platform connecting seekers with authentic experiences.</p>
                        <p>Sarah partnered with tech entrepreneur Miguel Rodriguez and wellness expert Dr. Aisha Johnson to create a platform that would democratize access to transformative experiences. They envisioned a world where anyone could find their path to mindfulness, regardless of their background or location.</p>
                        <p>"We wanted to create something that wasn't just another booking platform," explains Miguel. "We wanted to build a community where people could find genuine connection and transformation—a field where minds could grow and flourish."</p>
                    </div>
                </div>
            </section>

            <section class="story-section fade-in-section">
                <div class="story-header">
                    <h2>Our Journey</h2>
                    <p>The path from idea to global community.</p>
                </div>
                <div class="journey-animation">
                    <div class="journey-path"></div>
                    <div class="journey-milestone" style="left: 10%;" id="milestone1"></div>
                    <div class="journey-label top" style="left: 10%; transform: translateX(-50%);">2018: The Idea</div>
                    <div class="journey-milestone" style="left: 30%;" id="milestone2"></div>
                    <div class="journey-label bottom" style="left: 30%; transform: translateX(-50%);">2019: First Prototype</div>
                    <div class="journey-milestone" style="left: 50%;" id="milestone3"></div>
                    <div class="journey-label top" style="left: 50%; transform: translateX(-50%);">2020: Beta Launch</div>
                    <div class="journey-milestone" style="left: 70%;" id="milestone4"></div>
                    <div class="journey-label bottom" style="left: 70%; transform: translateX(-50%);">2021: Global Expansion</div>
                    <div class="journey-milestone" style="left: 90%;" id="milestone5"></div>
                    <div class="journey-label top" style="left: 90%; transform: translateX(-50%);">2023: 1M+ Users</div>
                    <div class="journey-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                </div>
                <div class="story-timeline">
                    <div class="timeline-line"></div>
                    <div class="timeline-item fade-in-section">
                        <div class="timeline-content">
                            <h3>The First Retreat</h3>
                            <p>In early 2019, we organized our first MindField-facilitated retreat in Joshua Tree with just 12 participants. The transformative experiences shared there confirmed we were on the right path.</p>
                        </div>
                        <div class="timeline-dot"></div>
                    </div>
                    <div class="timeline-item fade-in-section">
                        <div class="timeline-content">
                            <h3>Pandemic Pivot</h3>
                            <p>When COVID-19 hit in 2020, we quickly adapted to offer virtual retreats, reaching people who were isolated but desperately needed connection and mindfulness practices.</p>
                        </div>
                        <div class="timeline-dot"></div>
                    </div>
                    <div class="timeline-item fade-in-section">
                        <div class="timeline-content">
                            <h3>Global Expansion</h3>
                            <p>By 2021, we had expanded to 25 countries, partnering with local guides and retreat centers to offer authentic experiences rooted in local traditions and wisdom.</p>
                        </div>
                        <div class="timeline-dot"></div>
                    </div>
                    <div class="timeline-item fade-in-section">
                        <div class="timeline-content">
                            <h3>Community Impact</h3>
                            <p>In 2022, we launched our "Field of Good" initiative, which has funded 50+ community wellness programs in underserved areas around the world.</p>
                        </div>
                        <div class="timeline-dot"></div>
                    </div>
                </div>
            </section>

            <section class="values-section fade-in-section">
                <div class="container">
                    <h2>Our Core Values</h2>
                    <p>The principles that guide everything we do at MindField.</p>
                    <div class="values-container">
                        <div class="value-card fade-in-section">
                            <div class="value-icon">
                                <i class="fas fa-heart"></i>
                            </div>
                            <h3>Authentic Connection</h3>
                            <p>We believe in creating spaces for genuine human connection, where people can be their true selves without judgment.</p>
                        </div>
                        <div class="value-card fade-in-section">
                            <div class="value-icon">
                                <i class="fas fa-globe-americas"></i>
                            </div>
                            <h3>Global Wisdom</h3>
                            <p>We honor diverse traditions and approaches to mindfulness, recognizing that wisdom comes in many forms across cultures.</p>
                        </div>
                        <div class="value-card fade-in-section">
                            <div class="value-icon">
                                <i class="fas fa-hands-helping"></i>
                            </div>
                            <h3>Accessible Transformation</h3>
                            <p>We're committed to making transformative experiences accessible to everyone, regardless of background or circumstance.</p>
                        </div>
                    </div>
                </div>
            </section>

            <section class="team-section fade-in-section">
                <div class="container">
                    <h2>The Founders</h2>
                    <p>The visionaries who brought MindField to life.</p>
                    <div class="team-container">
                        <div class="team-member fade-in-section">
                            <div class="team-photo">
                                <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80" alt="Sarah Chen">
                            </div>
                            <div class="team-info">
                                <h3>Sarah Chen</h3>
                                <p>Founder & CEO</p>
                                <div class="team-social">
                                    <a href="#"><i class="fab fa-linkedin"></i></a>
                                    <a href="#"><i class="fab fa-twitter"></i></a>
                                    <a href="#"><i class="fab fa-instagram"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="team-member fade-in-section">
                            <div class="team-photo">
                                <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80" alt="Miguel Rodriguez">
                            </div>
                            <div class="team-info">
                                <h3>Miguel Rodriguez</h3>
                                <p>Co-Founder & CTO</p>
                                <div class="team-social">
                                    <a href="#"><i class="fab fa-linkedin"></i></a>
                                    <a href="#"><i class="fab fa-github"></i></a>
                                    <a href="#"><i class="fab fa-twitter"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="team-member fade-in-section">
                            <div class="team-photo">
                                <img src="https://images.unsplash.com/photo-1551836022-d5d88e9218df?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80" alt="Dr. Aisha Johnson">
                            </div>
                            <div class="team-info">
                                <h3>Dr. Aisha Johnson</h3>
                                <p>Co-Founder & Wellness Director</p>
                                <div class="team-social">
                                    <a href="#"><i class="fab fa-linkedin"></i></a>
                                    <a href="#"><i class="fab fa-twitter"></i></a>
                                    <a href="#"><i class="fab fa-instagram"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <section class="cta-section">
            <div class="cta-content">
                <h2 class="animate-in">Join Our Journey</h2>
                <p class="animate-in">Whether you're seeking transformation, ready to guide others, or have a space to share, there's a place for you in our community.</p>
                <div class="cta-buttons animate-in">
                    <a href="groups.html" class="btn primary-btn">Find Your Path</a>
                    <a href="browse.html" class="btn secondary-btn">Explore Retreats</a>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <div class="logo"><a href="index.html">MindField</a></div>
                <p>Transform Your Life</p>
            </div>
            <div class="footer-links">
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="about.html">About Us</a>
                    <a href="#">Careers</a>
                    <a href="#">Press</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Blog</a>
                    <a href="#">Community</a>
                    <a href="guides.html">Guides</a>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <a href="#">Help Center</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 MindField. All rights reserved.</p>
            <div class="social-icons">
                <a href="#"><i class="fab fa-facebook"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-storage.js"></script>

    <!-- App Scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/security.js"></script>
    <script src="js/performance.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/database.js"></script>
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/simple-test-user.js"></script>

    <!-- About Page Specific Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Fade in sections on scroll
            const fadeInSections = document.querySelectorAll('.fade-in-section');

            function checkFade() {
                fadeInSections.forEach(section => {
                    const sectionTop = section.getBoundingClientRect().top;
                    const windowHeight = window.innerHeight;

                    if (sectionTop < windowHeight * 0.85) {
                        section.classList.add('is-visible');
                    }
                });
            }

            // Initial check
            checkFade();

            // Check on scroll
            window.addEventListener('scroll', checkFade);

            // Animate journey milestones
            const milestones = document.querySelectorAll('.journey-milestone');
            let currentMilestone = 0;

            function activateMilestone() {
                if (currentMilestone < milestones.length) {
                    milestones[currentMilestone].classList.add('active');
                    currentMilestone++;
                    setTimeout(activateMilestone, 1000);
                }
            }

            // Start milestone animation when the section is visible
            const journeySection = document.querySelector('.journey-animation');

            function checkJourneyVisibility() {
                const journeyTop = journeySection.getBoundingClientRect().top;
                const windowHeight = window.innerHeight;

                if (journeyTop < windowHeight * 0.7 && currentMilestone === 0) {
                    activateMilestone();
                    window.removeEventListener('scroll', checkJourneyVisibility);
                }
            }

            window.addEventListener('scroll', checkJourneyVisibility);
            checkJourneyVisibility(); // Initial check
        });
    </script>
</body>
</html>
