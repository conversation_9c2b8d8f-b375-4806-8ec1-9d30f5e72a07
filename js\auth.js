// Authentication functionality for MindField

document.addEventListener('DOMContentLoaded', function() {
  // Check if user is logged in
  checkAuthState();

  // Setup auth UI elements
  setupAuthUI();

  // Setup auth event listeners
  setupAuthListeners();
});

// Check authentication state
function checkAuthState() {
  // Check if Firebase Auth is available
  if (typeof auth !== 'undefined' && auth) {
    auth.onAuthStateChanged(user => {
      if (user) {
        // User is signed in
        console.log('User logged in:', user.email);
        updateUIForLoggedInUser(user);
        loadUserData(user.uid);
      } else {
        // User is signed out
        console.log('User logged out');
        updateUIForLoggedOutUser();
      }
    });
  } else {
    console.log('Firebase Auth not available - continuing as guest');
    // Handle as guest user
    updateUIForLoggedOutUser();
  }
}

// Update UI for logged in user
function updateUIForLoggedInUser(user) {
  // Show profile icon, hide login/signup buttons
  const profileElements = document.querySelectorAll('.profile-icon');
  const authButtons = document.querySelectorAll('.auth-button');

  profileElements.forEach(el => {
    el.style.display = 'flex'; // Use flex to match the navbar styling
    // Update profile icon with user initial or photo if available
    const profileIcon = el.querySelector('i');
    if (profileIcon) {
      if (user.photoURL) {
        // Replace icon with user photo
        profileIcon.outerHTML = `<img src="${user.photoURL}" alt="${user.displayName || user.email}" class="user-avatar">`;
      } else if (user.displayName) {
        // Replace icon with user initial
        profileIcon.outerHTML = `<div class="user-initial">${user.displayName.charAt(0)}</div>`;
      }
    }
  });

  authButtons.forEach(el => {
    el.style.display = 'none';
  });

  // Update user name in profile if on profile page
  const profileName = document.querySelector('.profile-info h2');
  if (profileName) {
    profileName.textContent = `Welcome, ${user.displayName || user.email.split('@')[0]}`;
  }
}

// Update UI for logged out user
function updateUIForLoggedOutUser() {
  // Keep profile icon visible, show login/signup buttons
  const authButtons = document.querySelectorAll('.auth-button');

  // Make sure profile icon is visible (no longer hiding it)
  const profileElements = document.querySelectorAll('.profile-icon');
  profileElements.forEach(el => {
    el.style.display = 'flex'; // Use flex to match the navbar styling
  });

  authButtons.forEach(el => {
    el.style.display = 'block';
  });

  // Redirect to login if on protected page that requires login
  const currentPage = window.location.pathname;
  if (currentPage.includes('profile.html')) {
    window.location.href = 'login.html';
  }
  // Allow browse.html to be accessed without login
}

// Setup auth UI elements
function setupAuthUI() {
  // Add login/signup modal to all pages if not already present
  if (!document.getElementById('auth-modal')) {
    const authModal = document.createElement('div');
    authModal.id = 'auth-modal';
    authModal.className = 'modal';
    authModal.innerHTML = `
      <div class="modal-content">
        <span class="close-modal">&times;</span>
        <div class="auth-tabs">
          <button class="auth-tab active" data-tab="login">Login</button>
          <button class="auth-tab" data-tab="signup">Sign Up</button>
        </div>

        <div id="login-form" class="auth-form active">
          <h2>Welcome Back</h2>
          <div class="form-group">
            <label for="login-email">Email</label>
            <input type="email" id="login-email" required>
          </div>
          <div class="form-group">
            <label for="login-password">Password</label>
            <input type="password" id="login-password" required>
          </div>
          <div class="form-actions">
            <button type="button" id="login-button" class="btn btn-primary">Login</button>
          </div>
          <div class="auth-options">
            <a href="#" id="forgot-password">Forgot Password?</a>
          </div>
          <div class="social-auth">
            <button type="button" id="google-login" class="btn btn-outline">
              <i class="fab fa-google"></i> Continue with Google
            </button>
          </div>
        </div>

        <div id="signup-form" class="auth-form">
          <h2>Create Account</h2>
          <div class="form-group">
            <label for="signup-name">Full Name</label>
            <input type="text" id="signup-name" required>
          </div>
          <div class="form-group">
            <label for="signup-email">Email</label>
            <input type="email" id="signup-email" required>
          </div>
          <div class="form-group">
            <label for="signup-password">Password</label>
            <input type="password" id="signup-password" required>
            <small>Password must be at least 8 characters with a number and special character</small>
          </div>
          <div class="form-group">
            <label for="signup-confirm">Confirm Password</label>
            <input type="password" id="signup-confirm" required>
          </div>
          <div class="form-actions">
            <button type="button" id="signup-button" class="btn btn-primary">Sign Up</button>
          </div>
          <div class="social-auth">
            <button type="button" id="google-signup" class="btn btn-outline">
              <i class="fab fa-google"></i> Sign Up with Google
            </button>
          </div>
        </div>

        <div id="reset-form" class="auth-form">
          <h2>Reset Password</h2>
          <p>Enter your email address and we'll send you a link to reset your password.</p>
          <div class="form-group">
            <label for="reset-email">Email</label>
            <input type="email" id="reset-email" required>
          </div>
          <div class="form-actions">
            <button type="button" id="reset-button" class="btn btn-primary">Send Reset Link</button>
            <button type="button" id="back-to-login" class="btn btn-outline">Back to Login</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(authModal);

    // Add auth buttons to header if not already present
    const navLinks = document.querySelector('.nav-links');
    if (navLinks && !document.querySelector('.auth-button')) {
      const authButtons = document.createElement('div');
      authButtons.className = 'auth-buttons';
      authButtons.innerHTML = `
        <a href="#" class="auth-button btn btn-outline" id="login-nav">Login</a>
        <a href="#" class="auth-button btn btn-primary" id="signup-nav">Sign Up</a>
      `;
      navLinks.parentNode.insertBefore(authButtons, navLinks.nextSibling);
    }
  }
}

// Setup auth event listeners
function setupAuthListeners() {
  // Modal open/close
  const modal = document.getElementById('auth-modal');
  const closeModal = document.querySelector('.close-modal');
  const loginNav = document.getElementById('login-nav');
  const signupNav = document.getElementById('signup-nav');

  if (modal && closeModal) {
    // Close modal
    closeModal.addEventListener('click', () => {
      modal.style.display = 'none';
    });

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.style.display = 'none';
      }
    });
  }

  if (loginNav) {
    loginNav.addEventListener('click', (e) => {
      e.preventDefault();
      showAuthForm('login');
      modal.style.display = 'block';
    });
  }

  if (signupNav) {
    signupNav.addEventListener('click', (e) => {
      e.preventDefault();
      showAuthForm('signup');
      modal.style.display = 'block';
    });
  }

  // Tab switching
  const authTabs = document.querySelectorAll('.auth-tab');
  authTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const tabName = tab.getAttribute('data-tab');
      showAuthForm(tabName);
    });
  });

  // Forgot password
  const forgotPassword = document.getElementById('forgot-password');
  if (forgotPassword) {
    forgotPassword.addEventListener('click', (e) => {
      e.preventDefault();
      showAuthForm('reset');
    });
  }

  // Back to login
  const backToLogin = document.getElementById('back-to-login');
  if (backToLogin) {
    backToLogin.addEventListener('click', () => {
      showAuthForm('login');
    });
  }

  // Login form submission
  const loginButton = document.getElementById('login-button');
  if (loginButton) {
    loginButton.addEventListener('click', () => {
      const email = document.getElementById('login-email').value;
      const password = document.getElementById('login-password').value;

      if (email && password) {
        loginWithEmail(email, password);
      } else {
        showAuthError('Please enter both email and password');
      }
    });
  }

  // Signup form submission
  const signupButton = document.getElementById('signup-button');
  if (signupButton) {
    signupButton.addEventListener('click', () => {
      const name = document.getElementById('signup-name').value;
      const email = document.getElementById('signup-email').value;
      const password = document.getElementById('signup-password').value;
      const confirm = document.getElementById('signup-confirm').value;

      if (name && email && password && confirm) {
        if (password === confirm) {
          if (isPasswordStrong(password)) {
            signupWithEmail(name, email, password);
          } else {
            showAuthError('Password must be at least 8 characters with a number and special character');
          }
        } else {
          showAuthError('Passwords do not match');
        }
      } else {
        showAuthError('Please fill in all fields');
      }
    });
  }

  // Reset password
  const resetButton = document.getElementById('reset-button');
  if (resetButton) {
    resetButton.addEventListener('click', () => {
      const email = document.getElementById('reset-email').value;

      if (email) {
        resetPassword(email);
      } else {
        showAuthError('Please enter your email address');
      }
    });
  }

  // Google authentication
  const googleLogin = document.getElementById('google-login');
  const googleSignup = document.getElementById('google-signup');

  if (googleLogin) {
    googleLogin.addEventListener('click', () => {
      signInWithGoogle();
    });
  }

  if (googleSignup) {
    googleSignup.addEventListener('click', () => {
      signInWithGoogle();
    });
  }

  // Logout
  const logoutButton = document.getElementById('logout-button');
  if (logoutButton) {
    logoutButton.addEventListener('click', () => {
      logout();
    });
  }
}

// Show specific auth form
function showAuthForm(formName) {
  // Update tabs
  const authTabs = document.querySelectorAll('.auth-tab');
  authTabs.forEach(tab => {
    if (tab.getAttribute('data-tab') === formName) {
      tab.classList.add('active');
    } else {
      tab.classList.remove('active');
    }
  });

  // Show selected form, hide others
  const authForms = document.querySelectorAll('.auth-form');
  authForms.forEach(form => {
    form.classList.remove('active');
  });

  const selectedForm = document.getElementById(`${formName}-form`);
  if (selectedForm) {
    selectedForm.classList.add('active');
  }

  // Show/hide tabs for reset password
  const authTabsContainer = document.querySelector('.auth-tabs');
  if (formName === 'reset') {
    authTabsContainer.style.display = 'none';
  } else {
    authTabsContainer.style.display = 'flex';
  }
}

// Show auth error message
function showAuthError(message) {
  // Remove any existing error messages
  const existingError = document.querySelector('.auth-error');
  if (existingError) {
    existingError.remove();
  }

  // Create and add new error message
  const errorElement = document.createElement('div');
  errorElement.className = 'auth-error';
  errorElement.textContent = message;

  const activeForm = document.querySelector('.auth-form.active');
  if (activeForm) {
    activeForm.insertBefore(errorElement, activeForm.querySelector('.form-actions'));
  }
}

// Show auth success message
function showAuthSuccess(message) {
  // Remove any existing messages
  const existingMessage = document.querySelector('.auth-message');
  if (existingMessage) {
    existingMessage.remove();
  }

  // Create and add new success message
  const messageElement = document.createElement('div');
  messageElement.className = 'auth-message success';
  messageElement.textContent = message;

  const activeForm = document.querySelector('.auth-form.active');
  if (activeForm) {
    activeForm.insertBefore(messageElement, activeForm.querySelector('.form-actions'));
  }
}

// Check if password is strong
function isPasswordStrong(password) {
  // At least 8 characters, 1 number, 1 special character
  const regex = /^(?=.*[0-9])(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]{8,}$/;
  return regex.test(password);
}

// Login with email and password
function loginWithEmail(email, password) {
  const loginButton = document.getElementById('login-button');

  // Show loading state
  loginButton.classList.add('loading');
  loginButton.disabled = true;

  // Clear any existing errors
  const existingError = document.querySelector('.auth-error');
  if (existingError) existingError.remove();

  auth.signInWithEmailAndPassword(email, password)
    .then((userCredential) => {
      // Show success message
      if (window.errorHandler) {
        window.errorHandler.showNotification('Successfully logged in!', 'success');
      }

      // Close modal
      document.getElementById('auth-modal').style.display = 'none';

      // Clear form
      document.getElementById('login-email').value = '';
      document.getElementById('login-password').value = '';

      // Redirect to profile or previous page
      const urlParams = new URLSearchParams(window.location.search);
      const redirectTo = urlParams.get('redirect') || 'profile.html';
      window.location.href = redirectTo;
    })
    .catch((error) => {
      console.error('Login error:', error);
      const errorMessage = window.errorHandler ?
        window.errorHandler.getAuthErrorMessage(error.code) :
        'Login failed. Please try again.';
      showAuthError(errorMessage);
    })
    .finally(() => {
      // Remove loading state
      loginButton.classList.remove('loading');
      loginButton.disabled = false;
    });
}

// Sign up with email and password
function signupWithEmail(name, email, password) {
  auth.createUserWithEmailAndPassword(email, password)
    .then((userCredential) => {
      // Update profile with name
      return userCredential.user.updateProfile({
        displayName: name
      }).then(() => {
        // Create user document in Firestore
        return db.collection('users').doc(userCredential.user.uid).set({
          name: name,
          email: email,
          createdAt: firebase.firestore.FieldValue.serverTimestamp(),
          groups: []
        });
      });
    })
    .then(() => {
      // Close modal
      document.getElementById('auth-modal').style.display = 'none';

      // Clear form
      document.getElementById('signup-name').value = '';
      document.getElementById('signup-email').value = '';
      document.getElementById('signup-password').value = '';
      document.getElementById('signup-confirm').value = '';
    })
    .catch((error) => {
      console.error('Signup error:', error);
      showAuthError(getAuthErrorMessage(error.code));
    });
}

// Sign in with Google
function signInWithGoogle() {
  const provider = new firebase.auth.GoogleAuthProvider();

  auth.signInWithPopup(provider)
    .then((result) => {
      const user = result.user;

      // Check if this is a new user
      const isNewUser = result.additionalUserInfo.isNewUser;

      if (isNewUser) {
        // Create user document in Firestore
        return db.collection('users').doc(user.uid).set({
          name: user.displayName,
          email: user.email,
          photoURL: user.photoURL,
          createdAt: firebase.firestore.FieldValue.serverTimestamp(),
          groups: []
        });
      }
    })
    .then(() => {
      // Close modal
      document.getElementById('auth-modal').style.display = 'none';
    })
    .catch((error) => {
      console.error('Google sign-in error:', error);
      showAuthError(getAuthErrorMessage(error.code));
    });
}

// Reset password
function resetPassword(email) {
  auth.sendPasswordResetEmail(email)
    .then(() => {
      showAuthSuccess(`Password reset email sent to ${email}`);

      // Clear form
      document.getElementById('reset-email').value = '';

      // Return to login after 3 seconds
      setTimeout(() => {
        showAuthForm('login');
      }, 3000);
    })
    .catch((error) => {
      console.error('Password reset error:', error);
      showAuthError(getAuthErrorMessage(error.code));
    });
}

// Logout
function logout() {
  auth.signOut()
    .then(() => {
      // Redirect to home page
      window.location.href = 'index.html';
    })
    .catch((error) => {
      console.error('Logout error:', error);
    });
}

// Load user data from Firestore
function loadUserData(userId) {
  // Check if Firestore is available
  if (typeof db !== 'undefined' && db) {
    db.collection('users').doc(userId).get()
      .then((doc) => {
        if (doc.exists) {
          const userData = doc.data();

          // Update UI with user data
          updateUIWithUserData(userData);

          // Store user data in session storage for quick access
          sessionStorage.setItem('userData', JSON.stringify(userData));
        } else {
          console.log('No user data found');
        }
      })
      .catch((error) => {
        console.error('Error loading user data:', error);
      });
  } else {
    console.log('Firestore not available - cannot load user data');
  }
}

// Update UI with user data
function updateUIWithUserData(userData) {
  // Update profile page if on profile page
  if (window.location.pathname.includes('profile.html')) {
    // Update user info
    const profileName = document.querySelector('.profile-info h2');
    if (profileName) {
      profileName.textContent = `Welcome, ${userData.name}`;
    }

    const profileEmail = document.querySelector('.profile-info p');
    if (profileEmail) {
      const joinDate = userData.createdAt ? new Date(userData.createdAt.toDate()).getFullYear() : new Date().getFullYear();
      profileEmail.textContent = `Member since ${joinDate}`;
    }

    // Update form fields
    const nameInput = document.getElementById('full-name');
    const emailInput = document.getElementById('email');
    const bioInput = document.getElementById('bio');
    const locationInput = document.getElementById('location');

    if (nameInput) nameInput.value = userData.name || '';
    if (emailInput) emailInput.value = userData.email || '';
    if (bioInput) bioInput.value = userData.bio || '';
    if (locationInput) locationInput.value = userData.location || '';

    // Update group memberships
    updateGroupMemberships(userData.groups || []);
  }
}

// Update group memberships UI
function updateGroupMemberships(groups) {
  const groupCards = document.querySelectorAll('.group-membership-card');

  groupCards.forEach(card => {
    const groupTitle = card.querySelector('h2').textContent.toLowerCase();
    let groupType = '';

    if (groupTitle.includes('seeker')) {
      groupType = 'seeker';
    } else if (groupTitle.includes('guide')) {
      groupType = 'guide';
    } else if (groupTitle.includes('host')) {
      groupType = 'host';
    }

    const isActive = groups.includes(groupType);
    const statusElement = card.querySelector('.membership-status');
    const buttonElement = card.querySelector('.group-card-footer button');

    if (statusElement) {
      if (isActive) {
        statusElement.className = 'membership-status active';
        statusElement.innerHTML = '<i class="fas fa-check-circle"></i> Active';
      } else {
        statusElement.className = 'membership-status inactive';
        statusElement.innerHTML = '<i class="fas fa-circle"></i> Inactive';
      }
    }

    if (buttonElement) {
      if (isActive) {
        buttonElement.className = 'btn btn-outline';
        buttonElement.textContent = 'Manage Preferences';
      } else {
        buttonElement.className = 'btn btn-primary';
        buttonElement.textContent = `Become a ${groupType.charAt(0).toUpperCase() + groupType.slice(1)}`;

        // Add event listener to join group
        buttonElement.addEventListener('click', () => {
          joinGroup(groupType);
        });
      }
    }
  });
}

// Join a group
function joinGroup(groupType) {
  const user = auth.currentUser;

  if (user) {
    // Update user document in Firestore
    db.collection('users').doc(user.uid).update({
      groups: firebase.firestore.FieldValue.arrayUnion(groupType)
    })
    .then(() => {
      // Reload user data
      loadUserData(user.uid);

      // Show success message
      const successMessage = document.createElement('div');
      successMessage.className = 'success-message fade-in';
      successMessage.innerHTML = `<i class="fas fa-check-circle"></i> You are now a ${groupType.charAt(0).toUpperCase() + groupType.slice(1)}!`;

      document.querySelector('.profile-content').appendChild(successMessage);

      // Remove success message after delay
      setTimeout(() => {
        successMessage.classList.remove('fade-in');
        successMessage.classList.add('fade-out');

        setTimeout(() => {
          successMessage.remove();
        }, 500);
      }, 3000);
    })
    .catch((error) => {
      console.error('Error joining group:', error);
    });
  }
}

// Get user-friendly error message
function getAuthErrorMessage(errorCode) {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'No account found with this email address';
    case 'auth/wrong-password':
      return 'Incorrect password';
    case 'auth/email-already-in-use':
      return 'An account with this email already exists';
    case 'auth/weak-password':
      return 'Password is too weak';
    case 'auth/invalid-email':
      return 'Invalid email address';
    case 'auth/account-exists-with-different-credential':
      return 'An account already exists with the same email address but different sign-in credentials';
    case 'auth/operation-not-allowed':
      return 'This operation is not allowed';
    case 'auth/too-many-requests':
      return 'Too many unsuccessful login attempts. Please try again later';
    default:
      return 'An error occurred. Please try again';
  }
}
