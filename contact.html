<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindField - Contact Us</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="contact-page">
    <header>
        <nav class="navbar">
            <div class="logo"><a href="index.html">MindField</a></div>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="about.html">About</a>
                <a href="curated-memories.html">Curated</a>
                <a href="groups.html">Groups</a>
                <a href="browse.html">Browse</a>
                <a href="contact.html" class="active">Contact</a>
            </div>
            <div class="profile-icon">
                <a href="login.html"><i class="fas fa-user-circle"></i></a>
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <main class="contact-main">
        <!-- Hero Section -->
        <section class="contact-hero">
            <div class="container">
                <h1 class="animate-in">Get in Touch</h1>
                <p class="animate-in">We're here to help you on your mindfulness journey. Reach out to us anytime.</p>
            </div>
        </section>

        <!-- Contact Content -->
        <section class="contact-content">
            <div class="container">
                <div class="contact-grid">
                    <!-- Contact Form -->
                    <div class="contact-form-section animate-in">
                        <h2>Send us a Message</h2>
                        <form class="contact-form" id="contactForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="firstName">First Name *</label>
                                    <input type="text" id="firstName" name="firstName" required>
                                </div>
                                <div class="form-group">
                                    <label for="lastName">Last Name *</label>
                                    <input type="text" id="lastName" name="lastName" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone">
                            </div>
                            <div class="form-group">
                                <label for="subject">Subject *</label>
                                <select id="subject" name="subject" required>
                                    <option value="">Select a topic</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="retreat">Retreat Information</option>
                                    <option value="guide">Become a Guide</option>
                                    <option value="host">Host a Retreat</option>
                                    <option value="booking">Booking Support</option>
                                    <option value="technical">Technical Support</option>
                                    <option value="partnership">Partnership Opportunities</option>
                                    <option value="media">Media & Press</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="message">Message *</label>
                                <textarea id="message" name="message" rows="6" required placeholder="Tell us how we can help you..."></textarea>
                            </div>
                            <div class="form-group checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="newsletter" name="newsletter">
                                    <span class="checkmark"></span>
                                    Subscribe to our newsletter for retreat updates and mindfulness tips
                                </label>
                            </div>
                            <button type="submit" class="btn btn-primary">Send Message</button>
                        </form>
                    </div>

                    <!-- Contact Information -->
                    <div class="contact-info-section animate-in">
                        <h2>Contact Information</h2>
                        
                        <div class="contact-info-grid">
                            <div class="contact-info-item">
                                <div class="contact-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="contact-details">
                                    <h3>Email Us</h3>
                                    <p><EMAIL></p>
                                    <p class="contact-note">We typically respond within 24 hours</p>
                                </div>
                            </div>

                            <div class="contact-info-item">
                                <div class="contact-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div class="contact-details">
                                    <h3>Call Us</h3>
                                    <p>+****************</p>
                                    <p class="contact-note">Mon-Fri, 9AM-6PM PST</p>
                                </div>
                            </div>

                            <div class="contact-info-item">
                                <div class="contact-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="contact-details">
                                    <h3>Visit Us</h3>
                                    <p>123 Mindfulness Way<br>San Francisco, CA 94102</p>
                                    <p class="contact-note">By appointment only</p>
                                </div>
                            </div>

                            <div class="contact-info-item">
                                <div class="contact-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="contact-details">
                                    <h3>Business Hours</h3>
                                    <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                                    <p>Saturday: 10:00 AM - 4:00 PM</p>
                                    <p>Sunday: Closed</p>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Links -->
                        <div class="quick-links">
                            <h3>Quick Links</h3>
                            <div class="quick-links-grid">
                                <a href="browse.html" class="quick-link">
                                    <i class="fas fa-search"></i>
                                    <span>Find Retreats</span>
                                </a>
                                <a href="groups.html" class="quick-link">
                                    <i class="fas fa-users"></i>
                                    <span>Join Groups</span>
                                </a>
                                <a href="#" class="quick-link">
                                    <i class="fas fa-question-circle"></i>
                                    <span>FAQ</span>
                                </a>
                                <a href="#" class="quick-link">
                                    <i class="fas fa-headset"></i>
                                    <span>Support Center</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="faq-section">
            <div class="container">
                <h2 class="animate-in">Frequently Asked Questions</h2>
                <div class="faq-grid">
                    <div class="faq-item animate-in">
                        <h3>How do I book a retreat?</h3>
                        <p>Browse our available retreats, select your preferred dates, and complete the booking process. You'll receive confirmation within 24 hours.</p>
                    </div>
                    <div class="faq-item animate-in">
                        <h3>What's your cancellation policy?</h3>
                        <p>We offer flexible cancellation up to 14 days before your retreat start date. Please review our full terms and conditions for details.</p>
                    </div>
                    <div class="faq-item animate-in">
                        <h3>Can I become a retreat guide?</h3>
                        <p>Yes! We welcome experienced practitioners. Visit our Groups page to learn about guide opportunities and application requirements.</p>
                    </div>
                    <div class="faq-item animate-in">
                        <h3>Do you offer group discounts?</h3>
                        <p>We offer special rates for groups of 4 or more. Contact us directly to discuss group booking options and pricing.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <div class="logo"><a href="index.html">MindField</a></div>
                <p>Transform Your Life</p>
            </div>
            <div class="footer-links">
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="about.html">About Us</a>
                    <a href="#">Careers</a>
                    <a href="#">Press</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Blog</a>
                    <a href="#">Community</a>
                    <a href="curated-memories.html">Curated</a>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <a href="#">Help Center</a>
                    <a href="contact.html">Contact Us</a>
                    <a href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 MindField. All rights reserved.</p>
            <div class="social-icons">
                <a href="#"><i class="fab fa-facebook"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>

    <!-- App Scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/error-handler.js"></script>
    <script src="js/form-validator.js"></script>
    <script src="js/accessibility.js"></script>
    <script src="js/performance-monitor.js"></script>
    <script src="js/security.js"></script>
    <script src="js/performance.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/database.js"></script>
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/simple-test-user.js"></script>

    <script>
        // Contact form functionality
        document.addEventListener('DOMContentLoaded', function() {
            const contactForm = document.getElementById('contactForm');

            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Get form data
                    const formData = new FormData(contactForm);
                    const data = Object.fromEntries(formData);

                    // Show loading state
                    const submitBtn = contactForm.querySelector('button[type="submit"]');
                    const originalText = submitBtn.textContent;
                    submitBtn.textContent = 'Sending...';
                    submitBtn.disabled = true;

                    // Simulate form submission (replace with actual API call)
                    setTimeout(() => {
                        // Show success message
                        showNotification('Thank you! Your message has been sent successfully. We\'ll get back to you within 24 hours.', 'success');

                        // Reset form
                        contactForm.reset();

                        // Reset button
                        submitBtn.textContent = originalText;
                        submitBtn.disabled = false;

                        // Log form data for development
                        console.log('Contact form submitted:', data);
                    }, 2000);
                });
            }

            // Form validation
            const requiredFields = contactForm.querySelectorAll('[required]');
            requiredFields.forEach(field => {
                field.addEventListener('blur', function() {
                    validateField(this);
                });

                field.addEventListener('input', function() {
                    if (this.classList.contains('error')) {
                        validateField(this);
                    }
                });
            });

            function validateField(field) {
                const value = field.value.trim();
                const fieldGroup = field.closest('.form-group');

                // Remove existing error
                fieldGroup.classList.remove('error');
                const existingError = fieldGroup.querySelector('.error-message');
                if (existingError) {
                    existingError.remove();
                }

                // Validate
                let isValid = true;
                let errorMessage = '';

                if (field.hasAttribute('required') && !value) {
                    isValid = false;
                    errorMessage = 'This field is required';
                } else if (field.type === 'email' && value && !isValidEmail(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid email address';
                }

                if (!isValid) {
                    fieldGroup.classList.add('error');
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'error-message';
                    errorDiv.textContent = errorMessage;
                    fieldGroup.appendChild(errorDiv);
                }

                return isValid;
            }

            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            function showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.innerHTML = `
                    <div class="notification-content">
                        <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
                        <span>${message}</span>
                        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                // Add to page
                document.body.appendChild(notification);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 5000);
            }
        });
    </script>

    <style>
        /* Form validation styles */
        .form-group.error input,
        .form-group.error select,
        .form-group.error textarea {
            border-color: #e53e3e;
            box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
        }

        .error-message {
            color: #e53e3e;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        /* Notification styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            max-width: 400px;
            animation: slideInRight 0.3s ease-out;
        }

        .notification-success {
            border-left: 4px solid #38a169;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
        }

        .notification-content i:first-child {
            color: #38a169;
            font-size: 1.2rem;
        }

        .notification-close {
            background: none;
            border: none;
            color: #a0aec0;
            cursor: pointer;
            padding: 0.25rem;
            margin-left: auto;
        }

        .notification-close:hover {
            color: #718096;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</body>
</html>
