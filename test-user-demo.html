<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Demo</title>
    <style>
        :root {
            --primary-color: #6c63ff;
            --primary-light: #8a84ff;
            --primary-dark: #5046e5;
            --secondary-color: #63d1ff;
            --accent-color: #ff6584;
            --text-color: #333;
            --text-light: #777;
            --background-color: #fff;
            --background-light: #f9f9f9;
            --background-dark: #f0f0f0;
            --secondary-bg: #f8f9fa;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --error-color: #f44336;
            --border-radius: 8px;
            --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s ease;
            --container-width: 1200px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: var(--primary-color);
        }
        
        p {
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test User Demo</h1>
        <p>This page is used to test the test-user.js functionality.</p>
        <p>You should see a pink circular button in the bottom-right corner of the page.</p>
        <p>Click on it to open the test user panel.</p>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-storage.js"></script>

    <!-- App Scripts -->
    <script src="js/firebase-config.js"></script>
    <script>
        console.log('Page loaded');
        
        // Add a simple test function to check if the test user panel is visible
        function checkTestUserPanel() {
            const panel = document.querySelector('.test-user-panel');
            if (panel) {
                console.log('Test user panel found:', panel);
            } else {
                console.log('Test user panel not found');
            }
        }
        
        // Check after 2 seconds
        setTimeout(checkTestUserPanel, 2000);
    </script>
    <script src="js/test-user.js"></script>
</body>
</html>
