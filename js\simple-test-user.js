// Simple Test User Functionality for MindField
// This file provides a way to create and manage a test user with admin privileges

// Immediately invoke the setup function to ensure it runs
(function() {
  console.log('Simple test user script loaded');
  
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM loaded, setting up simple test user controls');
      setupSimpleTestUserControls();
    });
  } else {
    // DOM is already ready
    console.log('DOM already loaded, setting up simple test user controls');
    setupSimpleTestUserControls();
  }
})();

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Test@123',
  name: 'Test Admin',
  isAdmin: true
};

// Setup test user controls
function setupSimpleTestUserControls() {
  console.log('Setting up simple test user controls');
  
  // Create test user panel
  const testPanel = document.createElement('div');
  testPanel.className = 'simple-test-user-panel';
  testPanel.innerHTML = `
    <div class="simple-test-user-toggle">
      <span style="font-weight: bold;">TU</span>
    </div>
    <div class="simple-test-user-content">
      <h3>Test User Controls</h3>
      <p>Email: ${TEST_USER.email}</p>
      <p>Password: ${TEST_USER.password}</p>
      <div class="simple-test-user-actions">
        <button id="simple-login-test-user" class="btn secondary-btn">Login as Test User</button>
      </div>
      <div id="simple-test-user-status" class="simple-test-user-status"></div>
    </div>
  `;

  document.body.appendChild(testPanel);
  console.log('Simple test user panel appended to body');

  // Add styles for test user panel
  const style = document.createElement('style');
  style.textContent = `
    .simple-test-user-panel {
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 9999;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .simple-test-user-toggle {
      width: 50px;
      height: 50px;
      background-color: #ff6584; /* Using a bright color to make it more visible */
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      cursor: pointer;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
      transition: all 0.3s ease;
      font-size: 16px;
      border: 3px solid white;
    }

    .simple-test-user-toggle:hover {
      transform: scale(1.1);
    }

    .simple-test-user-content {
      display: none;
      background-color: white;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      margin-bottom: 10px;
      width: 300px;
      position: absolute;
      bottom: 60px;
      right: 0;
      border: 2px solid #ff6584;
    }

    .simple-test-user-content h3 {
      color: #6c63ff;
      margin-bottom: 1rem;
    }

    .simple-test-user-content p {
      margin-bottom: 0.5rem;
    }

    .simple-test-user-actions {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      margin-top: 1rem;
    }

    .btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .secondary-btn {
      background-color: #63d1ff;
      color: white;
    }

    .secondary-btn:hover {
      background-color: #4db8e5;
    }

    .simple-test-user-status {
      margin-top: 1rem;
      padding: 0.5rem;
      font-size: 0.9rem;
      border-radius: 8px;
      display: none;
    }
  `;

  document.head.appendChild(style);

  // Add event listeners
  const toggle = document.querySelector('.simple-test-user-toggle');
  const content = document.querySelector('.simple-test-user-content');

  toggle.addEventListener('click', () => {
    content.style.display = content.style.display === 'block' ? 'none' : 'block';
  });

  // Login as test user
  document.getElementById('simple-login-test-user').addEventListener('click', () => {
    // Simulate login
    localStorage.setItem('testUserLoggedIn', 'true');
    localStorage.setItem('testUserEmail', TEST_USER.email);
    localStorage.setItem('testUserName', TEST_USER.name);
    localStorage.setItem('testUserIsAdmin', TEST_USER.isAdmin);
    
    // Show success message
    const statusElement = document.getElementById('simple-test-user-status');
    statusElement.textContent = 'Logged in as test user!';
    statusElement.style.display = 'block';
    statusElement.style.backgroundColor = '#e8f5e9';
    statusElement.style.color = '#4caf50';
    
    // Redirect to index.html after 1 second
    setTimeout(() => {
      window.location.href = 'index.html';
    }, 1000);
  });
  
  console.log('All simple test user event listeners added');
}
