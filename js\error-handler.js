// Global error handling and user feedback system for MindField

class ErrorHandler {
  constructor() {
    this.setupGlobalErrorHandling();
    this.createNotificationContainer();
  }

  // Setup global error handling
  setupGlobalErrorHandling() {
    // Handle uncaught JavaScript errors
    window.addEventListener('error', (event) => {
      console.error('Global error:', event.error);

      // Only show error notifications to admin users
      if (this.isAdminUser()) {
        this.showNotification(`Admin Error: ${event.message} (${event.filename}:${event.lineno})`, 'error');
      }
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);

      // Only show error notifications to admin users
      if (this.isAdminUser()) {
        this.showNotification(`Admin Promise Error: ${event.reason.message || 'Unhandled rejection'}`, 'error');
      }

      event.preventDefault();
    });
  }

  // Check if current user is admin
  isAdminUser() {
    // Check for admin flag in localStorage
    const isAdmin = localStorage.getItem('mindfield_admin') === 'true';

    // Check for admin URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const adminParam = urlParams.get('admin') === 'true';

    // Check for admin email if user is logged in
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    const currentUser = window.firebase?.auth?.currentUser;
    const isAdminEmail = currentUser && adminEmails.includes(currentUser.email);

    return isAdmin || adminParam || isAdminEmail;
  }

  // Create notification container
  createNotificationContainer() {
    if (!document.getElementById('notification-container')) {
      const container = document.createElement('div');
      container.id = 'notification-container';
      container.className = 'notification-container';
      document.body.appendChild(container);
    }
  }

  // Show notification to user
  showNotification(message, type = 'info', duration = 5000) {
    const container = document.getElementById('notification-container');
    if (!container) return;

    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <i class="notification-icon ${this.getIconClass(type)}"></i>
        <span class="notification-message">${message}</span>
        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
          <i class="fas fa-times"></i>
        </button>
      </div>
    `;

    container.appendChild(notification);

    // Auto-remove after duration
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, duration);

    // Add entrance animation
    setTimeout(() => {
      notification.classList.add('show');
    }, 10);
  }

  // Get icon class based on notification type
  getIconClass(type) {
    const icons = {
      success: 'fas fa-check-circle',
      error: 'fas fa-exclamation-circle',
      warning: 'fas fa-exclamation-triangle',
      info: 'fas fa-info-circle'
    };
    return icons[type] || icons.info;
  }

  // Show loading state
  showLoading(element, message = 'Loading...') {
    if (!element) return;

    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = `
      <div class="loading-spinner">
        <div class="spinner"></div>
        <span class="loading-message">${message}</span>
      </div>
    `;

    element.style.position = 'relative';
    element.appendChild(loadingOverlay);
  }

  // Hide loading state
  hideLoading(element) {
    if (!element) return;

    const loadingOverlay = element.querySelector('.loading-overlay');
    if (loadingOverlay) {
      loadingOverlay.remove();
    }
  }

  // Handle Firebase auth errors
  getAuthErrorMessage(errorCode) {
    const errorMessages = {
      'auth/user-not-found': 'No account found with this email address.',
      'auth/wrong-password': 'Incorrect password. Please try again.',
      'auth/email-already-in-use': 'An account with this email already exists.',
      'auth/weak-password': 'Password should be at least 6 characters.',
      'auth/invalid-email': 'Please enter a valid email address.',
      'auth/too-many-requests': 'Too many failed attempts. Please try again later.',
      'auth/network-request-failed': 'Network error. Please check your connection.',
      'auth/popup-closed-by-user': 'Sign-in was cancelled.',
      'auth/cancelled-popup-request': 'Sign-in was cancelled.',
      'auth/popup-blocked': 'Pop-up was blocked. Please allow pop-ups for this site.'
    };

    return errorMessages[errorCode] || 'An error occurred. Please try again.';
  }

  // Handle network errors
  handleNetworkError(error) {
    if (!navigator.onLine) {
      this.showNotification('You appear to be offline. Please check your internet connection.', 'warning');
    } else {
      this.showNotification('Network error. Please try again.', 'error');
    }
    console.error('Network error:', error);
  }

  // Validate form data
  validateForm(formData, rules) {
    const errors = [];

    for (const [field, value] of Object.entries(formData)) {
      const rule = rules[field];
      if (!rule) continue;

      // Required field validation
      if (rule.required && (!value || value.trim() === '')) {
        errors.push(`${rule.label || field} is required.`);
        continue;
      }

      // Email validation
      if (rule.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          errors.push(`Please enter a valid ${rule.label || field}.`);
        }
      }

      // Password validation
      if (rule.type === 'password' && value) {
        if (value.length < (rule.minLength || 8)) {
          errors.push(`${rule.label || field} must be at least ${rule.minLength || 8} characters.`);
        }
        if (rule.requireSpecial && !/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
          errors.push(`${rule.label || field} must contain at least one special character.`);
        }
        if (rule.requireNumber && !/\d/.test(value)) {
          errors.push(`${rule.label || field} must contain at least one number.`);
        }
      }

      // Custom validation
      if (rule.validate && typeof rule.validate === 'function') {
        const customError = rule.validate(value);
        if (customError) {
          errors.push(customError);
        }
      }
    }

    return errors;
  }
}

// Initialize global error handler
const errorHandler = new ErrorHandler();

// Export for use in other modules
window.ErrorHandler = ErrorHandler;
window.errorHandler = errorHandler;
