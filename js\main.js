// Main JavaScript functionality for MindField

document.addEventListener('DOMContentLoaded', function() {
    // Initialize mobile menu
    initMobileMenu();

    // Initialize profile tabs
    initProfileTabs();

    // Initialize animations
    initAnimations();

    // Initialize price range slider
    initPriceRange();

    // Initialize view toggle
    initViewToggle();

    // Get URL parameters to handle navigation
    const urlParams = new URLSearchParams(window.location.search);
    const groupType = urlParams.get('group');
    const browseType = urlParams.get('type');

    // Handle group selection if parameter exists
    if (groupType) {
        highlightSelectedGroup(groupType);
    }

    // Handle browse type if parameter exists
    if (browseType) {
        updateBrowseTitle(browseType);
        loadResults(browseType);
    }
});

// Mobile menu functionality
function initMobileMenu() {
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('.nav-links');

    if (hamburger) {
        hamburger.addEventListener('click', function() {
            this.classList.toggle('active');

            if (navLinks) {
                navLinks.classList.toggle('active');

                // Add mobile menu styles dynamically
                if (navLinks.classList.contains('active')) {
                    navLinks.style.display = 'flex';
                    navLinks.style.flexDirection = 'column';
                    navLinks.style.position = 'absolute';
                    navLinks.style.top = '100%';
                    navLinks.style.left = '0';
                    navLinks.style.right = '0';
                    navLinks.style.backgroundColor = 'white';
                    navLinks.style.padding = '1rem';
                    navLinks.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
                    navLinks.style.zIndex = '1000';
                } else {
                    // Reset styles when menu is closed
                    setTimeout(() => {
                        if (!navLinks.classList.contains('active')) {
                            navLinks.removeAttribute('style');
                        }
                    }, 300);
                }
            }
        });
    }
}

// Profile tabs functionality
function initProfileTabs() {
    const tabLinks = document.querySelectorAll('.profile-nav-item');
    const tabs = document.querySelectorAll('.profile-tab');

    if (tabLinks.length && tabs.length) {
        tabLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all links
                tabLinks.forEach(item => item.classList.remove('active'));

                // Add active class to clicked link
                this.classList.add('active');

                // Get the tab to show
                const tabId = this.getAttribute('data-tab');

                // Hide all tabs
                tabs.forEach(tab => tab.classList.remove('active'));

                // Show the selected tab
                document.getElementById(tabId).classList.add('active');
            });
        });
    }
}

// Initialize animations
function initAnimations() {
    // Use Intersection Observer to trigger animations when elements are in view
    const animatedElements = document.querySelectorAll('.animate-in, .feature');

    if (animatedElements.length) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    // Unobserve after animation is triggered
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1
        });

        animatedElements.forEach(element => {
            observer.observe(element);
        });
    }

    // Add microinteractions to buttons
    const buttons = document.querySelectorAll('.btn, .primary-btn, .secondary-btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
            this.style.boxShadow = '0 5px 15px rgba(108, 99, 255, 0.3)';
            // Preserve button width settings
            this.style.setProperty('width', 'auto', 'important');
            this.style.setProperty('display', 'inline-block', 'important');
            this.style.setProperty('flex', 'none', 'important');
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
            // Preserve button width settings
            this.style.setProperty('width', 'auto', 'important');
            this.style.setProperty('display', 'inline-block', 'important');
            this.style.setProperty('flex', 'none', 'important');
        });

        button.addEventListener('mousedown', function() {
            this.style.transform = 'translateY(-1px)';
            // Preserve button width settings
            this.style.setProperty('width', 'auto', 'important');
            this.style.setProperty('display', 'inline-block', 'important');
            this.style.setProperty('flex', 'none', 'important');
        });

        button.addEventListener('mouseup', function() {
            this.style.transform = 'translateY(-3px)';
            // Preserve button width settings
            this.style.setProperty('width', 'auto', 'important');
            this.style.setProperty('display', 'inline-block', 'important');
            this.style.setProperty('flex', 'none', 'important');
        });
    });

    // Add parallax effect to hero section
    const hero = document.querySelector('.hero');
    if (hero) {
        window.addEventListener('scroll', function() {
            const scrollPosition = window.scrollY;
            if (scrollPosition < 600) {
                const heroContent = hero.querySelector('.hero-content');
                const heroImage = hero.querySelector('.hero-image');

                if (heroContent) heroContent.style.transform = `translateY(${scrollPosition * 0.1}px)`;
                if (heroImage) heroImage.style.transform = `translateY(${scrollPosition * 0.05}px)`;
            }
        });

        // Handle "Enter MindField" button click
        const enterButton = document.getElementById('enter-btn');
        if (enterButton) {
            enterButton.addEventListener('click', function() {
                window.location.href = 'groups.html';
            });
        }
    }
}

// Initialize price range slider
function initPriceRange() {
    const priceRange = document.getElementById('price-range');
    const priceValue = document.getElementById('price-value');

    if (priceRange && priceValue) {
        priceRange.addEventListener('input', function() {
            priceValue.textContent = '$' + this.value;
        });
    }
}

// Initialize view toggle
function initViewToggle() {
    const listViewBtn = document.getElementById('list-view');
    const gridViewBtn = document.getElementById('grid-view');
    const resultsList = document.getElementById('results-list');

    if (listViewBtn && gridViewBtn && resultsList) {
        listViewBtn.addEventListener('click', function() {
            listViewBtn.classList.add('active');
            gridViewBtn.classList.remove('active');
            resultsList.classList.remove('grid-view');
            resultsList.classList.add('list-view');
        });

        gridViewBtn.addEventListener('click', function() {
            gridViewBtn.classList.add('active');
            listViewBtn.classList.remove('active');
            resultsList.classList.remove('list-view');
            resultsList.classList.add('grid-view');
        });
    }
}

// Highlight selected group
function highlightSelectedGroup(groupType) {
    const groupCards = document.querySelectorAll('.group-card');

    if (groupCards.length) {
        groupCards.forEach(card => {
            if (card.id === 'group-' + groupType) {
                card.classList.add('selected');
                card.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    }
}

// Update browse title based on type
function updateBrowseTitle(type) {
    const browseTitle = document.getElementById('browse-title');

    if (browseTitle) {
        switch(type) {
            case 'retreats':
                browseTitle.textContent = 'Discover Retreats';
                break;
            case 'guides':
                browseTitle.textContent = 'Find Retreat Guides';
                break;
            case 'spaces':
                browseTitle.textContent = 'Explore Retreat Spaces';
                break;
            default:
                browseTitle.textContent = 'Browse MindField';
        }
    }
}

// Load results based on type
function loadResults(type) {
    const resultsList = document.getElementById('results-list');

    if (resultsList) {
        // Clear existing results
        resultsList.innerHTML = '';

        // Generate mock data based on type
        let results = [];

        switch(type) {
            case 'retreats':
                results = generateMockRetreats();
                break;
            case 'guides':
                results = generateMockGuides();
                break;
            case 'spaces':
                results = generateMockSpaces();
                break;
            default:
                results = generateMockRetreats();
        }

        // Add results to the list
        results.forEach(result => {
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item animate-in';
            resultItem.innerHTML = `
                <div class="result-image">
                    <img src="${result.image}" alt="${result.title}">
                </div>
                <div class="result-content">
                    <h3>${result.title}</h3>
                    <p class="result-location"><i class="fas fa-map-marker-alt"></i> ${result.location}</p>
                    <p class="result-description">${result.description}</p>
                    <div class="result-details">
                        ${result.details.map(detail => `<span><i class="${detail.icon}"></i> ${detail.text}</span>`).join('')}
                    </div>
                    <div class="result-price">
                        <span class="price">${result.price}</span>
                        <span class="price-period">${result.pricePeriod}</span>
                    </div>
                </div>
            `;

            resultsList.appendChild(resultItem);
        });

        // Initialize animations for new elements
        initAnimations();
    }
}

// Generate mock retreat data
function generateMockRetreats() {
    return [
        {
            id: 1,
            title: 'Mindfulness in the Mountains',
            location: 'Ubud, Bali',
            description: 'A transformative 7-day retreat combining mindfulness meditation, yoga, and nature immersion in the lush mountains of Bali.',
            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
            price: '$1,200',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'June 15-22, 2024' },
                { icon: 'fas fa-user', text: 'Led by Sarah Johnson' },
                { icon: 'fas fa-users', text: '8 spots left' },
                { icon: 'fas fa-star', text: '4.9 (28 reviews)' }
            ],
            coordinates: { lat: -8.409518, lng: 115.188919 }
        },
        {
            id: 2,
            title: 'Desert Silence Retreat',
            location: 'Sedona, Arizona',
            description: 'Experience profound silence and self-discovery in the mystical red rocks of Sedona. 5 days of meditation, hiking, and inner exploration.',
            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
            price: '$950',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'July 8-13, 2024' },
                { icon: 'fas fa-user', text: 'Led by Michael Chen' },
                { icon: 'fas fa-users', text: '12 spots left' },
                { icon: 'fas fa-star', text: '4.8 (45 reviews)' }
            ],
            coordinates: { lat: 34.8697, lng: -111.7610 }
        },
        {
            id: 3,
            title: 'Ocean Waves Yoga Retreat',
            location: 'Tulum, Mexico',
            description: 'Awaken your senses with daily yoga sessions by the ocean, cenote swimming, and Mayan healing ceremonies in beautiful Tulum.',
            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
            price: '$1,450',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'August 20-27, 2024' },
                { icon: 'fas fa-user', text: 'Led by Maria Rodriguez' },
                { icon: 'fas fa-users', text: '6 spots left' },
                { icon: 'fas fa-star', text: '4.9 (67 reviews)' }
            ],
            coordinates: { lat: 20.2114, lng: -87.4654 }
        },
        {
            id: 4,
            title: 'Alpine Meditation Sanctuary',
            location: 'Swiss Alps, Switzerland',
            description: 'Find peace in the pristine Swiss Alps with guided meditation, mountain hiking, and traditional wellness practices.',
            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
            price: '$2,100',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'September 5-12, 2024' },
                { icon: 'fas fa-user', text: 'Led by Hans Mueller' },
                { icon: 'fas fa-users', text: '15 spots left' },
                { icon: 'fas fa-star', text: '4.7 (23 reviews)' }
            ],
            coordinates: { lat: 46.8182, lng: 8.2275 }
        },
        {
            id: 5,
            title: 'Forest Bathing Experience',
            location: 'Kyoto, Japan',
            description: 'Immerse yourself in the ancient Japanese practice of Shinrin-yoku (forest bathing) combined with Zen meditation and tea ceremony.',
            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
            price: '$1,650',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'October 10-17, 2024' },
                { icon: 'fas fa-user', text: 'Led by Yuki Tanaka' },
                { icon: 'fas fa-users', text: '9 spots left' },
                { icon: 'fas fa-star', text: '4.9 (34 reviews)' }
            ],
            coordinates: { lat: 35.0116, lng: 135.7681 }
        },
        {
            id: 6,
            title: 'Coastal Wellness Retreat',
            location: 'Big Sur, California',
            description: 'Rejuvenate your mind and body with cliff-top yoga, organic farm-to-table meals, and therapeutic hot springs overlooking the Pacific.',
            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
            price: '$1,800',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'November 3-10, 2024' },
                { icon: 'fas fa-user', text: 'Led by Emma Thompson' },
                { icon: 'fas fa-users', text: '5 spots left' },
                { icon: 'fas fa-star', text: '4.8 (52 reviews)' }
            ],
            coordinates: { lat: 36.2704, lng: -121.8081 }
        },
        {
            id: 7,
            title: 'Himalayan Spiritual Journey',
            location: 'Rishikesh, India',
            description: 'Embark on a spiritual awakening in the yoga capital of the world. Includes Ganga Aarti, ashram visits, and authentic Hatha yoga.',
            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
            price: '$850',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'December 1-8, 2024' },
                { icon: 'fas fa-user', text: 'Led by Guru Ananda' },
                { icon: 'fas fa-users', text: '20 spots left' },
                { icon: 'fas fa-star', text: '4.9 (89 reviews)' }
            ],
            coordinates: { lat: 30.0869, lng: 78.2676 }
        },
        {
            id: 8,
            title: 'Northern Lights Meditation',
            location: 'Lapland, Finland',
            description: 'Witness the Aurora Borealis while practicing meditation in heated glass igloos. Includes reindeer encounters and sauna ceremonies.',
            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
            price: '$2,400',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'January 15-22, 2025' },
                { icon: 'fas fa-user', text: 'Led by Aino Virtanen' },
                { icon: 'fas fa-users', text: '7 spots left' },
                { icon: 'fas fa-star', text: '4.8 (16 reviews)' }
            ],
            coordinates: { lat: 68.9792, lng: 33.0724 }
        },
        {
            id: 9,
            title: 'Sahara Desert Awakening',
            location: 'Merzouga, Morocco',
            description: 'Experience the profound silence of the Sahara with camel trekking, Berber culture immersion, and stargazing meditation.',
            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
            price: '$1,350',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'February 12-19, 2025' },
                { icon: 'fas fa-user', text: 'Led by Amina Hassan' },
                { icon: 'fas fa-users', text: '11 spots left' },
                { icon: 'fas fa-star', text: '4.7 (31 reviews)' }
            ],
            coordinates: { lat: 31.0801, lng: -4.0133 }
        },
        {
            id: 10,
            title: 'Patagonia Wilderness Retreat',
            location: 'Torres del Paine, Chile',
            description: 'Connect with raw nature in Patagonia through hiking meditation, wildlife observation, and eco-conscious living practices.',
            image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
            price: '$1,950',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'March 8-15, 2025' },
                { icon: 'fas fa-user', text: 'Led by Carlos Mendoza' },
                { icon: 'fas fa-users', text: '14 spots left' },
                { icon: 'fas fa-star', text: '4.9 (42 reviews)' }
            ],
            coordinates: { lat: -50.9423, lng: -73.4068 }
        }
        {
            id: 2,
            title: 'Yoga by the Sea',
            location: 'Tulum, Mexico',
            description: 'Experience the perfect blend of yoga, meditation, and beach relaxation in this 5-day retreat.',
            image: 'https://via.placeholder.com/300x200',
            price: '$950',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'July 10-15, 2023' },
                { icon: 'fas fa-user', text: 'Led by Miguel Santos' },
                { icon: 'fas fa-users', text: '5 spots left' },
                { icon: 'fas fa-star', text: '4.8 (42 reviews)' }
            ],
            coordinates: { lat: 20.211841, lng: -87.465530 }
        },
        {
            id: 3,
            title: 'Spiritual Awakening Retreat',
            location: 'Sedona, Arizona',
            description: 'Connect with your inner self through guided meditations, energy healing, and nature walks.',
            image: 'https://via.placeholder.com/300x200',
            price: '$1,500',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'August 5-12, 2023' },
                { icon: 'fas fa-user', text: 'Led by Emma Clarke' },
                { icon: 'fas fa-users', text: '8 spots left' },
                { icon: 'fas fa-star', text: '5.0 (19 reviews)' }
            ],
            coordinates: { lat: 34.869691, lng: -111.760978 }
        },
        {
            id: 4,
            title: 'Zen Meditation Intensive',
            location: 'Kyoto, Japan',
            description: 'Immerse yourself in traditional Zen practices with daily meditation, mindful walks, and tea ceremonies.',
            image: 'https://via.placeholder.com/300x200',
            price: '$1,800',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'September 1-10, 2023' },
                { icon: 'fas fa-user', text: 'Led by Master Tanaka' },
                { icon: 'fas fa-users', text: '3 spots left' },
                { icon: 'fas fa-star', text: '4.9 (35 reviews)' }
            ],
            coordinates: { lat: 35.011665, lng: 135.768326 }
        },
        {
            id: 5,
            title: 'Wellness & Detox Retreat',
            location: 'Costa Rica',
            description: 'Rejuvenate your body and mind with organic meals, yoga, meditation, and spa treatments.',
            image: 'https://via.placeholder.com/300x200',
            price: '$2,200',
            pricePeriod: 'per person',
            details: [
                { icon: 'fas fa-calendar', text: 'October 15-22, 2023' },
                { icon: 'fas fa-user', text: 'Led by Dr. Lisa Martinez' },
                { icon: 'fas fa-users', text: '12 spots left' },
                { icon: 'fas fa-star', text: '4.7 (23 reviews)' }
            ],
            coordinates: { lat: 9.748917, lng: -83.753428 }
        }
    ];
}

// Generate mock guide data
function generateMockGuides() {
    return [
        {
            id: 1,
            title: 'Sarah Johnson',
            location: 'Bali, Indonesia',
            description: 'Certified yoga instructor and mindfulness coach with 10+ years of experience leading retreats worldwide.',
            image: 'https://via.placeholder.com/300x200',
            price: '$200',
            pricePeriod: 'per session',
            details: [
                { icon: 'fas fa-certificate', text: 'Yoga Alliance Certified' },
                { icon: 'fas fa-language', text: 'English, Spanish' },
                { icon: 'fas fa-history', text: '10+ years experience' },
                { icon: 'fas fa-star', text: '4.9 (56 reviews)' }
            ],
            coordinates: { lat: -8.409518, lng: 115.188919 }
        },
        {
            id: 2,
            title: 'Miguel Santos',
            location: 'Tulum, Mexico',
            description: 'Specializes in vinyasa flow and meditation. Creates transformative experiences that connect mind, body, and spirit.',
            image: 'https://via.placeholder.com/300x200',
            price: '$180',
            pricePeriod: 'per session',
            details: [
                { icon: 'fas fa-certificate', text: '500hr RYT Certified' },
                { icon: 'fas fa-language', text: 'English, Spanish, Portuguese' },
                { icon: 'fas fa-history', text: '8 years experience' },
                { icon: 'fas fa-star', text: '4.8 (42 reviews)' }
            ],
            coordinates: { lat: 20.211841, lng: -87.465530 }
        },
        {
            id: 3,
            title: 'Emma Clarke',
            location: 'Sedona, Arizona',
            description: 'Spiritual guide and energy healer. Facilitates deep inner journeys through meditation and nature connection.',
            image: 'https://via.placeholder.com/300x200',
            price: '$250',
            pricePeriod: 'per session',
            details: [
                { icon: 'fas fa-certificate', text: 'Certified Reiki Master' },
                { icon: 'fas fa-language', text: 'English' },
                { icon: 'fas fa-history', text: '15 years experience' },
                { icon: 'fas fa-star', text: '5.0 (38 reviews)' }
            ],
            coordinates: { lat: 34.869691, lng: -111.760978 }
        },
        {
            id: 4,
            title: 'Master Tanaka',
            location: 'Kyoto, Japan',
            description: 'Traditional Zen Buddhist monk offering authentic meditation instruction and spiritual guidance.',
            image: 'https://via.placeholder.com/300x200',
            price: '$300',
            pricePeriod: 'per session',
            details: [
                { icon: 'fas fa-certificate', text: 'Zen Master' },
                { icon: 'fas fa-language', text: 'Japanese, English' },
                { icon: 'fas fa-history', text: '25 years experience' },
                { icon: 'fas fa-star', text: '4.9 (35 reviews)' }
            ],
            coordinates: { lat: 35.011665, lng: 135.768326 }
        },
        {
            id: 5,
            title: 'Dr. Lisa Martinez',
            location: 'Costa Rica',
            description: 'Holistic health practitioner combining modern wellness science with ancient healing traditions.',
            image: 'https://via.placeholder.com/300x200',
            price: '$275',
            pricePeriod: 'per session',
            details: [
                { icon: 'fas fa-certificate', text: 'PhD in Holistic Health' },
                { icon: 'fas fa-language', text: 'English, Spanish' },
                { icon: 'fas fa-history', text: '12 years experience' },
                { icon: 'fas fa-star', text: '4.7 (49 reviews)' }
            ],
            coordinates: { lat: 9.748917, lng: -83.753428 }
        }
    ];
}

// Generate mock space data
function generateMockSpaces() {
    return [
        {
            id: 1,
            title: 'Mountain Sanctuary Retreat Center',
            location: 'Bali, Indonesia',
            description: 'Serene mountain retreat center with panoramic views, meditation halls, yoga studios, and organic gardens.',
            image: 'https://via.placeholder.com/300x200',
            price: '$3,500',
            pricePeriod: 'per week',
            details: [
                { icon: 'fas fa-users', text: 'Accommodates up to 20 guests' },
                { icon: 'fas fa-home', text: '10 private rooms, 2 dormitories' },
                { icon: 'fas fa-spa', text: 'Yoga studio, meditation hall' },
                { icon: 'fas fa-star', text: '4.9 (28 reviews)' }
            ],
            coordinates: { lat: -8.409518, lng: 115.188919 }
        },
        {
            id: 2,
            title: 'Beachfront Yoga Villa',
            location: 'Tulum, Mexico',
            description: 'Stunning beachfront property with open-air yoga pavilion, meditation spaces, and eco-friendly accommodations.',
            image: 'https://via.placeholder.com/300x200',
            price: '$4,200',
            pricePeriod: 'per week',
            details: [
                { icon: 'fas fa-users', text: 'Accommodates up to 16 guests' },
                { icon: 'fas fa-home', text: '8 private cabanas' },
                { icon: 'fas fa-spa', text: 'Beachfront yoga deck, pool' },
                { icon: 'fas fa-star', text: '4.8 (42 reviews)' }
            ],
            coordinates: { lat: 20.211841, lng: -87.465530 }
        },
        {
            id: 3,
            title: 'Red Rock Spiritual Center',
            location: 'Sedona, Arizona',
            description: 'Sacred space nestled among the red rocks with meditation gardens, labyrinth, and energy vortex sites.',
            image: 'https://via.placeholder.com/300x200',
            price: '$3,800',
            pricePeriod: 'per week',
            details: [
                { icon: 'fas fa-users', text: 'Accommodates up to 15 guests' },
                { icon: 'fas fa-home', text: '7 private rooms, 1 dormitory' },
                { icon: 'fas fa-spa', text: 'Meditation dome, crystal garden' },
                { icon: 'fas fa-star', text: '5.0 (19 reviews)' }
            ],
            coordinates: { lat: 34.869691, lng: -111.760978 }
        },
        {
            id: 4,
            title: 'Traditional Japanese Zen Temple',
            location: 'Kyoto, Japan',
            description: 'Authentic Zen temple offering traditional accommodations, meditation halls, and Japanese gardens.',
            image: 'https://via.placeholder.com/300x200',
            price: '$3,200',
            pricePeriod: 'per week',
            details: [
                { icon: 'fas fa-users', text: 'Accommodates up to 12 guests' },
                { icon: 'fas fa-home', text: '6 traditional tatami rooms' },
                { icon: 'fas fa-spa', text: 'Meditation hall, tea ceremony room' },
                { icon: 'fas fa-star', text: '4.9 (35 reviews)' }
            ],
            coordinates: { lat: 35.011665, lng: 135.768326 }
        },
        {
            id: 5,
            title: 'Rainforest Wellness Center',
            location: 'Costa Rica',
            description: 'Eco-friendly retreat center in the rainforest with organic farm, natural pools, and holistic spa.',
            image: 'https://via.placeholder.com/300x200',
            price: '$4,500',
            pricePeriod: 'per week',
            details: [
                { icon: 'fas fa-users', text: 'Accommodates up to 25 guests' },
                { icon: 'fas fa-home', text: '10 eco-cabins, 1 main house' },
                { icon: 'fas fa-spa', text: 'Yoga pavilion, natural pools, spa' },
                { icon: 'fas fa-star', text: '4.7 (23 reviews)' }
            ],
            coordinates: { lat: 9.748917, lng: -83.753428 }
        }
    ];
}
