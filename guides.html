<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindField - Guide Showcase</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Guide showcase specific styles */
        .guides-hero {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
            color: white;
            padding: 100px 0 50px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .guides-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .guides-hero p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto 2rem;
            line-height: 1.6;
        }

        .guides-container {
            max-width: var(--container-width);
            margin: 0 auto;
            padding: 50px 20px;
        }

        .guides-filter {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .filter-btn {
            background-color: var(--background-light);
            border: none;
            padding: 10px 20px;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .filter-btn:hover, .filter-btn.active {
            background-color: var(--primary-color);
            color: white;
        }

        .guides-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
        }

        .guide-card {
            background-color: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .guide-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .guide-retreat-image {
            width: 100%;
            height: 200px;
            overflow: hidden;
            position: relative;
        }

        .guide-retreat-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .guide-card:hover .guide-retreat-image img {
            transform: scale(1.05);
        }

        .guide-info {
            display: flex;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid var(--background-light);
        }

        .guide-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 15px;
            border: 3px solid var(--primary-light);
        }

        .guide-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .guide-details {
            flex: 1;
        }

        .guide-name {
            font-weight: 600;
            color: var(--primary-dark);
            margin-bottom: 5px;
            font-size: 1.1rem;
        }

        .guide-title {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .retreat-info {
            padding: 20px;
        }

        .retreat-name {
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: var(--text-color);
        }

        .retreat-description {
            color: var(--text-light);
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .retreat-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .retreat-location {
            display: flex;
            align-items: center;
        }

        .retreat-location i {
            margin-right: 5px;
            color: var(--primary-color);
        }

        .retreat-rating {
            display: flex;
            align-items: center;
        }

        .retreat-rating i {
            color: #FFD700;
            margin-right: 3px;
        }

        .featured-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background-color: var(--primary-color);
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            z-index: 2;
        }

        .load-more {
            display: block;
            margin: 40px auto 0;
            padding: 12px 30px;
            background-color: var(--primary-light);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .load-more:hover {
            background-color: var(--primary-dark);
        }

        .guide-cta {
            background-color: var(--background-light);
            padding: 80px 0;
            text-align: center;
            margin-top: 60px;
        }

        .guide-cta-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .guide-cta h2 {
            color: var(--primary-dark);
            font-size: 2.2rem;
            margin-bottom: 20px;
        }

        .guide-cta p {
            color: var(--text-light);
            font-size: 1.1rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .guides-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }
        }

        @media (max-width: 480px) {
            .guides-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body class="guides-page">
    <header>
        <nav class="navbar">
            <div class="logo"><a href="index.html">MindField</a></div>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="about.html">About</a>
                <a href="guides.html" class="active">Guides</a>
                <a href="groups.html">Groups</a>
                <a href="browse.html">Browse</a>
                <a href="index.html#contact">Contact</a>
            </div>
            <div class="profile-icon">
                <a href="login.html"><i class="fas fa-user-circle"></i></a>
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <main>
        <section class="guides-hero">
            <div class="container">
                <h1 class="animate-in">Guide Showcase</h1>
                <p class="animate-in">Discover our community of exceptional retreat guides and their transformative experiences.</p>
            </div>
        </section>

        <div class="guides-container">
            <div class="guides-filter">
                <button class="filter-btn active" data-filter="all">All Retreats</button>
                <button class="filter-btn" data-filter="meditation">Meditation</button>
                <button class="filter-btn" data-filter="yoga">Yoga</button>
                <button class="filter-btn" data-filter="wellness">Wellness</button>
                <button class="filter-btn" data-filter="nature">Nature</button>
                <button class="filter-btn" data-filter="spiritual">Spiritual</button>
            </div>

            <div class="guides-grid">
                <!-- Guide Card 1 -->
                <div class="guide-card animate-in" data-category="meditation spiritual">
                    <div class="guide-retreat-image">
                        <img src="https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Silent Meditation Retreat">
                        <div class="featured-badge">Featured</div>
                    </div>
                    <div class="guide-info">
                        <div class="guide-avatar">
                            <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=256&q=80" alt="Elena Rodriguez">
                        </div>
                        <div class="guide-details">
                            <div class="guide-name">Elena Rodriguez</div>
                            <div class="guide-title">Meditation Teacher & Spiritual Guide</div>
                        </div>
                    </div>
                    <div class="retreat-info">
                        <h3 class="retreat-name">Silent Meditation Retreat</h3>
                        <p class="retreat-description">A transformative 7-day journey into silence that helped participants discover their inner voice.</p>
                        <div class="retreat-meta">
                            <div class="retreat-location">
                                <i class="fas fa-map-marker-alt"></i> Bali, Indonesia
                            </div>
                            <div class="retreat-rating">
                                <i class="fas fa-star"></i> 4.9 (28 reviews)
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Guide Card 2 -->
                <div class="guide-card animate-in" data-category="yoga wellness">
                    <div class="guide-retreat-image">
                        <img src="https://images.unsplash.com/photo-1588286840104-8957b019727f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Yoga & Wellness Immersion">
                    </div>
                    <div class="guide-info">
                        <div class="guide-avatar">
                            <img src="https://images.unsplash.com/photo-1566492031773-4f4e44671857?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=256&q=80" alt="David Chen">
                        </div>
                        <div class="guide-details">
                            <div class="guide-name">David Chen</div>
                            <div class="guide-title">Yoga Instructor & Wellness Coach</div>
                        </div>
                    </div>
                    <div class="retreat-info">
                        <h3 class="retreat-name">Yoga & Wellness Immersion</h3>
                        <p class="retreat-description">An integrative approach to wellness through daily yoga, nutrition, and mindfulness practices.</p>
                        <div class="retreat-meta">
                            <div class="retreat-location">
                                <i class="fas fa-map-marker-alt"></i> Tulum, Mexico
                            </div>
                            <div class="retreat-rating">
                                <i class="fas fa-star"></i> 4.8 (42 reviews)
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Guide Card 3 -->
                <div class="guide-card animate-in" data-category="nature spiritual">
                    <div class="guide-retreat-image">
                        <img src="https://images.unsplash.com/photo-1518002171953-a080ee817e1f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Forest Bathing & Mindfulness">
                    </div>
                    <div class="guide-info">
                        <div class="guide-avatar">
                            <img src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=256&q=80" alt="Amara Johnson">
                        </div>
                        <div class="guide-details">
                            <div class="guide-name">Amara Johnson</div>
                            <div class="guide-title">Nature Therapy Guide</div>
                        </div>
                    </div>
                    <div class="retreat-info">
                        <h3 class="retreat-name">Forest Bathing & Mindfulness</h3>
                        <p class="retreat-description">Reconnect with nature and yourself through guided forest immersion and mindfulness practices.</p>
                        <div class="retreat-meta">
                            <div class="retreat-location">
                                <i class="fas fa-map-marker-alt"></i> Kyoto, Japan
                            </div>
                            <div class="retreat-rating">
                                <i class="fas fa-star"></i> 4.9 (35 reviews)
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Guide Card 4 -->
                <div class="guide-card animate-in" data-category="meditation wellness">
                    <div class="guide-retreat-image">
                        <img src="https://images.unsplash.com/photo-1536623975707-c4b3b2af565d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Mindfulness & Stress Reduction">
                    </div>
                    <div class="guide-info">
                        <div class="guide-avatar">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=256&q=80" alt="Michael Patel">
                        </div>
                        <div class="guide-details">
                            <div class="guide-name">Michael Patel</div>
                            <div class="guide-title">Mindfulness Coach & Psychologist</div>
                        </div>
                    </div>
                    <div class="retreat-info">
                        <h3 class="retreat-name">Mindfulness & Stress Reduction</h3>
                        <p class="retreat-description">Learn evidence-based techniques to manage stress and cultivate lasting inner peace.</p>
                        <div class="retreat-meta">
                            <div class="retreat-location">
                                <i class="fas fa-map-marker-alt"></i> Costa Rica
                            </div>
                            <div class="retreat-rating">
                                <i class="fas fa-star"></i> 4.7 (31 reviews)
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Guide Card 5 -->
                <div class="guide-card animate-in" data-category="yoga nature">
                    <div class="guide-retreat-image">
                        <img src="https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Mountain Yoga Retreat">
                    </div>
                    <div class="guide-info">
                        <div class="guide-avatar">
                            <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=256&q=80" alt="Sofia Martinez">
                        </div>
                        <div class="guide-details">
                            <div class="guide-name">Sofia Martinez</div>
                            <div class="guide-title">Mountain Yoga Specialist</div>
                        </div>
                    </div>
                    <div class="retreat-info">
                        <h3 class="retreat-name">Mountain Yoga Retreat</h3>
                        <p class="retreat-description">Practice yoga at altitude, surrounded by breathtaking mountain views and fresh alpine air.</p>
                        <div class="retreat-meta">
                            <div class="retreat-location">
                                <i class="fas fa-map-marker-alt"></i> Swiss Alps
                            </div>
                            <div class="retreat-rating">
                                <i class="fas fa-star"></i> 4.9 (24 reviews)
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Guide Card 6 -->
                <div class="guide-card animate-in" data-category="spiritual meditation">
                    <div class="guide-retreat-image">
                        <img src="https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Beachside Meditation & Healing">
                    </div>
                    <div class="guide-info">
                        <div class="guide-avatar">
                            <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=256&q=80" alt="James Wilson">
                        </div>
                        <div class="guide-details">
                            <div class="guide-name">James Wilson</div>
                            <div class="guide-title">Sound Healer & Meditation Guide</div>
                        </div>
                    </div>
                    <div class="retreat-info">
                        <h3 class="retreat-name">Beachside Meditation & Healing</h3>
                        <p class="retreat-description">Experience the healing power of sound meditation by the ocean for deep relaxation and renewal.</p>
                        <div class="retreat-meta">
                            <div class="retreat-location">
                                <i class="fas fa-map-marker-alt"></i> Goa, India
                            </div>
                            <div class="retreat-rating">
                                <i class="fas fa-star"></i> 4.8 (39 reviews)
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <button class="load-more">Load More</button>
        </div>

        <section class="guide-cta">
            <div class="guide-cta-content">
                <h2 class="animate-in">Ready to Share Your Wisdom?</h2>
                <p class="animate-in">Join our community of guides and lead transformative retreats for seekers from around the world.</p>
                <a href="guide-application.html" class="btn primary-btn animate-in">Become a Guide</a>
            </div>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <div class="logo"><a href="index.html">MindField</a></div>
                <p>Transform Your Life</p>
            </div>
            <div class="footer-links">
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="about.html">About Us</a>
                    <a href="#">Careers</a>
                    <a href="#">Press</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Blog</a>
                    <a href="#">Community</a>
                    <a href="guides.html">Guides</a>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <a href="#">Help Center</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 MindField. All rights reserved.</p>
            <div class="social-icons">
                <a href="#"><i class="fab fa-facebook"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-storage.js"></script>

    <!-- App Scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/security.js"></script>
    <script src="js/performance.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/database.js"></script>
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/simple-test-user.js"></script>

    <!-- Guides Page Specific Script -->
    <script src="js/guides.js"></script>
</body>
</html>
