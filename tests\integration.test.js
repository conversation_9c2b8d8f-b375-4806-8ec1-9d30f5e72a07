// Integration tests for MindField

testFramework.test('Integration - Authentication Flow', async () => {
  // Test that auth modal can be opened
  const loginButton = document.querySelector('#login-nav');
  if (loginButton) {
    testFramework.simulateClick(loginButton);
    
    await testFramework.wait(200);
    
    const authModal = document.querySelector('#auth-modal');
    testFramework.assertNotNull(authModal, 'Auth modal should exist');
    
    // Test modal visibility
    const modalStyle = window.getComputedStyle(authModal);
    testFramework.assertTrue(
      modalStyle.display === 'block' || authModal.style.display === 'block',
      'Auth modal should be visible after clicking login'
    );
    
    // Test form elements exist
    const loginForm = document.querySelector('#login-form');
    const emailInput = document.querySelector('#login-email');
    const passwordInput = document.querySelector('#login-password');
    
    testFramework.assertNotNull(loginForm, 'Login form should exist');
    testFramework.assertNotNull(emailInput, 'Email input should exist');
    testFramework.assertNotNull(passwordInput, 'Password input should exist');
    
    // Close modal
    const closeButton = document.querySelector('.close-modal');
    if (closeButton) {
      testFramework.simulateClick(closeButton);
      await testFramework.wait(100);
    }
  } else {
    testFramework.assertTrue(true, 'Login button not found - test skipped');
  }
});

testFramework.test('Integration - Form Validation with Error Handling', async () => {
  // Create a complete form with validation
  const form = testFramework.createElement('form', { class: 'test-integration-form' }, `
    <div class="form-group">
      <label for="integration-email">Email</label>
      <input type="email" id="integration-email" required>
    </div>
    <div class="form-group">
      <label for="integration-password">Password</label>
      <input type="password" id="integration-password" required>
    </div>
    <button type="submit" class="btn btn-primary">Submit</button>
  `);
  document.body.appendChild(form);
  
  const emailInput = form.querySelector('#integration-email');
  const passwordInput = form.querySelector('#integration-password');
  const submitButton = form.querySelector('button[type="submit"]');
  
  // Test invalid form submission
  testFramework.simulateInput(emailInput, 'invalid-email');
  testFramework.simulateInput(passwordInput, 'weak');
  
  // Trigger validation
  emailInput.dispatchEvent(new Event('blur'));
  passwordInput.dispatchEvent(new Event('blur'));
  
  await testFramework.wait(200);
  
  // Check validation states
  const emailGroup = emailInput.closest('.form-group');
  const passwordGroup = passwordInput.closest('.form-group');
  
  testFramework.assertTrue(emailGroup.classList.contains('error'), 'Email should show error state');
  testFramework.assertTrue(passwordGroup.classList.contains('error'), 'Password should show error state');
  
  // Test error messages
  const emailError = emailGroup.querySelector('.form-error');
  const passwordError = passwordGroup.querySelector('.form-error');
  
  testFramework.assertNotNull(emailError, 'Email error message should appear');
  testFramework.assertNotNull(passwordError, 'Password error message should appear');
  
  // Test form submission prevention
  let formSubmitted = false;
  form.addEventListener('submit', (e) => {
    e.preventDefault();
    formSubmitted = true;
  });
  
  testFramework.simulateClick(submitButton);
  await testFramework.wait(100);
  
  // In a real implementation, invalid forms should not submit
  // This test verifies the validation system is working
  
  // Cleanup
  document.body.removeChild(form);
});

testFramework.test('Integration - Notification System with User Actions', async () => {
  if (window.errorHandler) {
    // Test notification appears and can be dismissed
    window.errorHandler.showNotification('Test integration notification', 'info', 10000);
    
    await testFramework.wait(200);
    
    const notification = document.querySelector('.notification');
    testFramework.assertNotNull(notification, 'Notification should appear');
    
    // Test close button functionality
    const closeButton = notification.querySelector('.notification-close');
    testFramework.assertNotNull(closeButton, 'Close button should exist');
    
    testFramework.simulateClick(closeButton);
    
    await testFramework.wait(100);
    
    // Check if notification was removed
    const removedNotification = document.querySelector('.notification');
    testFramework.assert(
      !removedNotification || !removedNotification.parentElement,
      'Notification should be removed after clicking close'
    );
  } else {
    throw new Error('Error handler not available');
  }
});

testFramework.test('Integration - Accessibility with Form Validation', async () => {
  // Create form with accessibility features
  const form = testFramework.createElement('form', {}, `
    <fieldset>
      <legend class="sr-only">User Registration</legend>
      <div class="form-group">
        <label for="accessible-email">Email Address</label>
        <input type="email" id="accessible-email" required aria-describedby="email-help">
        <small id="email-help">We'll never share your email with anyone else.</small>
      </div>
    </fieldset>
  `);
  document.body.appendChild(form);
  
  const input = form.querySelector('#accessible-email');
  const helpText = form.querySelector('#email-help');
  const fieldset = form.querySelector('fieldset');
  const legend = form.querySelector('legend');
  
  // Test accessibility structure
  testFramework.assertNotNull(fieldset, 'Form should have fieldset');
  testFramework.assertNotNull(legend, 'Fieldset should have legend');
  testFramework.assertTrue(legend.classList.contains('sr-only'), 'Legend should be screen reader only');
  
  // Test aria-describedby
  testFramework.assertEqual(
    input.getAttribute('aria-describedby'),
    helpText.id,
    'Input should be described by help text'
  );
  
  // Test validation accessibility
  testFramework.simulateInput(input, 'invalid');
  input.dispatchEvent(new Event('blur'));
  
  await testFramework.wait(200);
  
  const errorElement = form.querySelector('.form-error');
  if (errorElement) {
    testFramework.assertNotNull(
      input.getAttribute('aria-describedby'),
      'Input should have aria-describedby for error'
    );
    testFramework.assertEqual(
      input.getAttribute('aria-invalid'),
      'true',
      'Input should have aria-invalid when error exists'
    );
  }
  
  // Cleanup
  document.body.removeChild(form);
});

testFramework.test('Integration - Performance with Real User Interactions', async () => {
  // Test performance optimizations work together
  if (window.debounce && window.getElement) {
    // Create search input with debounced handler
    const searchContainer = testFramework.createElement('div', {}, `
      <input type="search" id="integration-search" placeholder="Search...">
      <div id="search-results"></div>
    `);
    document.body.appendChild(searchContainer);
    
    const searchInput = searchContainer.querySelector('#integration-search');
    const resultsContainer = searchContainer.querySelector('#search-results');
    
    let searchCount = 0;
    const searchHandler = window.debounce(() => {
      searchCount++;
      resultsContainer.innerHTML = `<div>Search performed ${searchCount} times</div>`;
    }, 300);
    
    searchInput.addEventListener('input', searchHandler);
    
    // Simulate rapid typing
    testFramework.simulateInput(searchInput, 'a');
    await testFramework.wait(50);
    testFramework.simulateInput(searchInput, 'ab');
    await testFramework.wait(50);
    testFramework.simulateInput(searchInput, 'abc');
    await testFramework.wait(50);
    testFramework.simulateInput(searchInput, 'abcd');
    
    // Should not have searched yet due to debouncing
    testFramework.assertEqual(searchCount, 0, 'Search should be debounced');
    
    // Wait for debounce delay
    await testFramework.wait(350);
    
    // Should have searched only once
    testFramework.assertEqual(searchCount, 1, 'Search should execute only once after debounce');
    
    // Test element caching
    const cachedElement = window.getElement('#search-results');
    testFramework.assertEqual(cachedElement, resultsContainer, 'Element should be cached');
    
    // Cleanup
    document.body.removeChild(searchContainer);
  } else {
    throw new Error('Performance utilities not available');
  }
});

testFramework.test('Integration - Mobile Menu Functionality', async () => {
  // Test mobile menu toggle
  const hamburger = document.querySelector('.hamburger');
  const navLinks = document.querySelector('.nav-links');
  
  if (hamburger && navLinks) {
    // Test initial state
    testFramework.assertFalse(hamburger.classList.contains('active'), 'Hamburger should not be active initially');
    
    // Test menu toggle
    testFramework.simulateClick(hamburger);
    
    await testFramework.wait(100);
    
    testFramework.assertTrue(hamburger.classList.contains('active'), 'Hamburger should be active after click');
    testFramework.assertTrue(navLinks.classList.contains('active'), 'Nav links should be active after click');
    
    // Test keyboard accessibility
    testFramework.assertEqual(hamburger.getAttribute('role'), 'button', 'Hamburger should have button role');
    testFramework.assertNotNull(hamburger.getAttribute('aria-label'), 'Hamburger should have aria-label');
    
    // Test close menu
    testFramework.simulateClick(hamburger);
    
    await testFramework.wait(100);
    
    testFramework.assertFalse(hamburger.classList.contains('active'), 'Hamburger should not be active after second click');
  } else {
    testFramework.assertTrue(true, 'Mobile menu elements not found - test skipped');
  }
});

testFramework.test('Integration - Loading States with Error Recovery', async () => {
  if (window.errorHandler) {
    // Create test button
    const testButton = testFramework.createElement('button', { 
      class: 'btn btn-primary',
      id: 'integration-loading-btn'
    }, 'Test Action');
    document.body.appendChild(testButton);
    
    // Simulate async operation with loading state
    testButton.addEventListener('click', async () => {
      // Show loading
      testButton.classList.add('loading');
      testButton.disabled = true;
      
      try {
        // Simulate async operation
        await testFramework.wait(500);
        
        // Simulate success
        window.errorHandler.showNotification('Operation completed successfully!', 'success');
      } catch (error) {
        // Simulate error
        window.errorHandler.showNotification('Operation failed. Please try again.', 'error');
      } finally {
        // Remove loading state
        testButton.classList.remove('loading');
        testButton.disabled = false;
      }
    });
    
    // Test loading state
    testFramework.simulateClick(testButton);
    
    await testFramework.wait(100);
    
    testFramework.assertTrue(testButton.classList.contains('loading'), 'Button should show loading state');
    testFramework.assertTrue(testButton.disabled, 'Button should be disabled during loading');
    
    // Wait for operation to complete
    await testFramework.wait(600);
    
    testFramework.assertFalse(testButton.classList.contains('loading'), 'Loading state should be removed');
    testFramework.assertFalse(testButton.disabled, 'Button should be enabled after operation');
    
    // Check for success notification
    const successNotification = document.querySelector('.notification-success');
    testFramework.assertNotNull(successNotification, 'Success notification should appear');
    
    // Cleanup
    document.body.removeChild(testButton);
    if (successNotification) successNotification.remove();
  } else {
    throw new Error('Error handler not available');
  }
});
