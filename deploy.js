// Deployment script for MindField app

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const config = {
  projectId: 'mindfield-app',
  firebaseConfig: {
    apiKey: "YOUR_API_KEY",
    authDomain: "mindfield-app.firebaseapp.com",
    projectId: "mindfield-app",
    storageBucket: "mindfield-app.appspot.com",
    messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
    appId: "YOUR_APP_ID",
    measurementId: "YOUR_MEASUREMENT_ID"
  },
  stripePublicKey: 'pk_test_YOUR_STRIPE_PUBLIC_KEY',
  stripeSecretKey: 'sk_test_YOUR_STRIPE_SECRET_KEY'
};

// Main function
async function deploy() {
  try {
    console.log('Starting MindField deployment...');
    
    // 1. Update Firebase configuration
    updateFirebaseConfig();
    
    // 2. Update Stripe configuration
    updateStripeConfig();
    
    // 3. Install Firebase CLI if not already installed
    installFirebaseCLI();
    
    // 4. Initialize Firebase project
    initializeFirebaseProject();
    
    // 5. Deploy to Firebase
    deployToFirebase();
    
    console.log('Deployment completed successfully!');
    console.log(`Your app is now live at: https://${config.projectId}.web.app`);
  } catch (error) {
    console.error('Deployment failed:', error);
    process.exit(1);
  }
}

// Update Firebase configuration
function updateFirebaseConfig() {
  console.log('Updating Firebase configuration...');
  
  const configPath = path.join(__dirname, 'js', 'firebase-config.js');
  
  if (!fs.existsSync(configPath)) {
    throw new Error(`Firebase config file not found at ${configPath}`);
  }
  
  let configContent = fs.readFileSync(configPath, 'utf8');
  
  // Replace placeholder config with actual config
  configContent = configContent.replace(
    /const firebaseConfig = \{[\s\S]*?\};/,
    `const firebaseConfig = ${JSON.stringify(config.firebaseConfig, null, 2)};`
  );
  
  fs.writeFileSync(configPath, configContent);
  console.log('Firebase configuration updated successfully.');
}

// Update Stripe configuration
function updateStripeConfig() {
  console.log('Updating Stripe configuration...');
  
  const paymentPath = path.join(__dirname, 'js', 'payment.js');
  
  if (!fs.existsSync(paymentPath)) {
    throw new Error(`Payment file not found at ${paymentPath}`);
  }
  
  let paymentContent = fs.readFileSync(paymentPath, 'utf8');
  
  // Replace placeholder Stripe public key with actual key
  paymentContent = paymentContent.replace(
    /const stripePublicKey = ['"].*?['"]/,
    `const stripePublicKey = '${config.stripePublicKey}'`
  );
  
  fs.writeFileSync(paymentPath, paymentContent);
  console.log('Stripe configuration updated successfully.');
}

// Install Firebase CLI
function installFirebaseCLI() {
  try {
    // Check if Firebase CLI is installed
    execSync('firebase --version', { stdio: 'ignore' });
    console.log('Firebase CLI is already installed.');
  } catch (error) {
    console.log('Installing Firebase CLI...');
    execSync('npm install -g firebase-tools', { stdio: 'inherit' });
    console.log('Firebase CLI installed successfully.');
  }
}

// Initialize Firebase project
function initializeFirebaseProject() {
  console.log('Initializing Firebase project...');
  
  // Check if firebase.json exists
  if (fs.existsSync(path.join(__dirname, 'firebase.json'))) {
    console.log('Firebase project already initialized.');
    return;
  }
  
  // Create firebase.json
  const firebaseConfig = {
    hosting: {
      public: './',
      ignore: [
        'firebase.json',
        '**/.*',
        '**/node_modules/**',
        'deploy.js',
        'README.md'
      ],
      rewrites: [
        {
          source: '**',
          destination: '/index.html'
        }
      ]
    }
  };
  
  fs.writeFileSync(
    path.join(__dirname, 'firebase.json'),
    JSON.stringify(firebaseConfig, null, 2)
  );
  
  // Create .firebaserc
  const firebaserc = {
    projects: {
      default: config.projectId
    }
  };
  
  fs.writeFileSync(
    path.join(__dirname, '.firebaserc'),
    JSON.stringify(firebaserc, null, 2)
  );
  
  console.log('Firebase project initialized successfully.');
}

// Deploy to Firebase
function deployToFirebase() {
  console.log('Deploying to Firebase...');
  
  try {
    // Login to Firebase (if not already logged in)
    execSync('firebase login --interactive', { stdio: 'inherit' });
    
    // Deploy to Firebase
    execSync('firebase deploy', { stdio: 'inherit' });
    
    console.log('Firebase deployment completed successfully.');
  } catch (error) {
    throw new Error(`Firebase deployment failed: ${error.message}`);
  }
}

// Run deployment
deploy().catch(console.error);
