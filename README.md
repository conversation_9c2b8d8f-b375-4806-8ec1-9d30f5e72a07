# MindField - Yoga and Meditation Retreat Platform

MindField is a modern, mobile-first web application that connects three types of users:
- **Group A**: People looking for yoga and meditation retreats
- **Group B**: Guides who lead retreats
- **Group C**: Hosts who provide spaces for retreats

## Project Structure

```
MindField/
├── index.html           # Landing page
├── groups.html          # Group selection page
├── browse.html          # Browse/search page with map
├── profile.html         # User profile page
├── css/
│   ├── styles.css       # Main stylesheet
│   └── animations.css   # Micro-animations
├── js/
│   ├── main.js          # Main JavaScript functionality
│   ├── map.js           # Map integration
│   └── animations.js    # Animation controls
└── assets/              # Directory for images and SVGs
```

## Features

### Landing Page
- Hero section with headline, description, and CTA buttons
- Three alternating feature sections highlighting each user group
- Final CTA section with trust elements

### Group Selection
- Three vertical sections for each user group
- Visual selection with relevant imagery

### Browse/Search
- Split layout with results on left, map on right
- Interactive map showing locations
- Filtering options
- List/grid view toggle

### User Profile
- Dashboard with stats and activity
- Personal information management
- Group membership management
- Bookings, listings, and payments

## Technologies Used

- HTML5
- CSS3 (with custom animations)
- JavaScript (vanilla, no frameworks)
- Leaflet.js for maps

## Getting Started

### Local Development

1. Clone this repository
2. Open `index.html` in your browser
3. Navigate through the application

### Production Deployment

1. Create a Firebase account at [firebase.google.com](https://firebase.google.com)
2. Create a new Firebase project
3. Enable Firebase Authentication, Firestore, and Hosting
4. Update the Firebase configuration in `js/firebase-config.js` with your project details
5. Create a Stripe account at [stripe.com](https://stripe.com)
6. Update the Stripe public key in `js/payment.js`
7. Run the deployment script:

```
node deploy.js
```

8. Your app will be deployed to `https://your-project-id.web.app`

### Security Considerations

- The Firebase configuration in `js/firebase-config.js` contains API keys that are meant to be public. Firebase security is handled through security rules on the server.
- Stripe payment processing is handled securely through the Stripe SDK. No credit card information is stored on your servers.
- For additional security, consider implementing Firebase Security Rules for your Firestore database and Storage.

## Browser Compatibility

This application is designed to work on all modern browsers and is fully responsive for mobile devices.

## Future Enhancements

- User authentication
- Real-time notifications
- Booking system
- Payment processing
- Reviews and ratings
