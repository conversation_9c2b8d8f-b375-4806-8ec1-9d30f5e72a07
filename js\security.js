// Security functionality for MindField

// Initialize security measures
document.addEventListener('DOMContentLoaded', function() {
  // Add CSRF protection
  addCSRFProtection();
  
  // Add content security policy
  addContentSecurityPolicy();
  
  // Add XSS protection
  addXSSProtection();
  
  // Add input validation
  addInputValidation();
  
  // Add secure storage
  initSecureStorage();
});

// Add CSRF protection
function addCSRFProtection() {
  // Generate CSRF token
  const csrfToken = generateCSRFToken();
  
  // Store token in localStorage
  localStorage.setItem('csrfToken', csrfToken);
  
  // Add token to all forms
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    // Skip forms that already have a CSRF token
    if (!form.querySelector('input[name="csrf_token"]')) {
      const csrfInput = document.createElement('input');
      csrfInput.type = 'hidden';
      csrfInput.name = 'csrf_token';
      csrfInput.value = csrfToken;
      form.appendChild(csrfInput);
    }
  });
  
  // Add token to all fetch/XHR requests
  const originalFetch = window.fetch;
  window.fetch = function(url, options = {}) {
    // Create headers if not exist
    if (!options.headers) {
      options.headers = {};
    }
    
    // Add CSRF token to headers
    options.headers['X-CSRF-Token'] = csrfToken;
    
    return originalFetch.call(this, url, options);
  };
  
  // Add token to XHR requests
  const originalOpen = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function() {
    const result = originalOpen.apply(this, arguments);
    this.setRequestHeader('X-CSRF-Token', csrfToken);
    return result;
  };
}

// Generate CSRF token
function generateCSRFToken() {
  // Generate a random string for CSRF token
  const array = new Uint8Array(16);
  window.crypto.getRandomValues(array);
  return Array.from(array, byte => ('0' + (byte & 0xFF).toString(16)).slice(-2)).join('');
}

// Add Content Security Policy
function addContentSecurityPolicy() {
  // In a production environment, this would be set in HTTP headers
  // For client-side only, we can add a meta tag
  
  const meta = document.createElement('meta');
  meta.httpEquiv = 'Content-Security-Policy';
  meta.content = "default-src 'self'; script-src 'self' https://cdnjs.cloudflare.com https://js.stripe.com; style-src 'self' https://cdnjs.cloudflare.com 'unsafe-inline'; img-src 'self' data: https://via.placeholder.com; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self' https://api.stripe.com https://firestore.googleapis.com https://identitytoolkit.googleapis.com; frame-src 'self' https://js.stripe.com; object-src 'none'";
  
  document.head.appendChild(meta);
}

// Add XSS protection
function addXSSProtection() {
  // Sanitize input function
  window.sanitizeInput = function(input) {
    if (!input) return '';
    
    // Create a temporary element
    const temp = document.createElement('div');
    temp.textContent = input;
    return temp.innerHTML;
  };
  
  // Sanitize HTML function
  window.sanitizeHTML = function(html) {
    if (!html) return '';
    
    // Create a temporary element
    const temp = document.createElement('div');
    temp.innerHTML = html;
    
    // Remove potentially dangerous elements and attributes
    const dangerousTags = ['script', 'iframe', 'object', 'embed', 'form'];
    const dangerousAttrs = ['onerror', 'onload', 'onclick', 'onmouseover', 'onmouseout', 'onkeydown', 'onkeyup', 'onkeypress'];
    
    // Remove dangerous tags
    dangerousTags.forEach(tag => {
      const elements = temp.getElementsByTagName(tag);
      while (elements.length > 0) {
        elements[0].parentNode.removeChild(elements[0]);
      }
    });
    
    // Remove dangerous attributes
    const allElements = temp.getElementsByTagName('*');
    for (let i = 0; i < allElements.length; i++) {
      const element = allElements[i];
      dangerousAttrs.forEach(attr => {
        if (element.hasAttribute(attr)) {
          element.removeAttribute(attr);
        }
      });
      
      // Remove javascript: URLs
      if (element.hasAttribute('href')) {
        const href = element.getAttribute('href');
        if (href.toLowerCase().startsWith('javascript:')) {
          element.setAttribute('href', '#');
        }
      }
      
      if (element.hasAttribute('src')) {
        const src = element.getAttribute('src');
        if (src.toLowerCase().startsWith('javascript:')) {
          element.removeAttribute('src');
        }
      }
    }
    
    return temp.innerHTML;
  };
  
  // Override innerHTML to sanitize content
  const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
  Object.defineProperty(Element.prototype, 'innerHTML', {
    set(value) {
      const sanitized = window.sanitizeHTML(value);
      originalInnerHTML.set.call(this, sanitized);
    },
    get() {
      return originalInnerHTML.get.call(this);
    }
  });
}

// Add input validation
function addInputValidation() {
  // Email validation
  window.validateEmail = function(email) {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  };
  
  // Password strength validation
  window.validatePasswordStrength = function(password) {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
    const re = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return re.test(password);
  };
  
  // Add validation to all forms
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    form.addEventListener('submit', function(e) {
      // Get all required inputs
      const requiredInputs = form.querySelectorAll('[required]');
      let isValid = true;
      
      requiredInputs.forEach(input => {
        // Remove any existing error messages
        const existingError = input.parentNode.querySelector('.input-error');
        if (existingError) {
          existingError.remove();
        }
        
        // Check if input is empty
        if (!input.value.trim()) {
          isValid = false;
          showInputError(input, 'This field is required');
        }
        
        // Validate email inputs
        if (input.type === 'email' && input.value.trim() && !validateEmail(input.value)) {
          isValid = false;
          showInputError(input, 'Please enter a valid email address');
        }
        
        // Validate password inputs
        if (input.type === 'password' && input.id.includes('signup') && input.value.trim() && !validatePasswordStrength(input.value)) {
          isValid = false;
          showInputError(input, 'Password must be at least 8 characters with uppercase, lowercase, number, and special character');
        }
      });
      
      // Check password confirmation
      const password = form.querySelector('#signup-password');
      const confirm = form.querySelector('#signup-confirm');
      if (password && confirm && password.value !== confirm.value) {
        isValid = false;
        showInputError(confirm, 'Passwords do not match');
      }
      
      // Prevent form submission if not valid
      if (!isValid) {
        e.preventDefault();
      }
    });
  });
}

// Show input error message
function showInputError(input, message) {
  const errorElement = document.createElement('div');
  errorElement.className = 'input-error';
  errorElement.textContent = message;
  
  input.parentNode.appendChild(errorElement);
  input.classList.add('input-invalid');
  
  // Remove error when input changes
  input.addEventListener('input', function() {
    const error = input.parentNode.querySelector('.input-error');
    if (error) {
      error.remove();
    }
    input.classList.remove('input-invalid');
  });
}

// Initialize secure storage
function initSecureStorage() {
  // Create a secure storage object that encrypts sensitive data
  window.secureStorage = {
    // Set item with optional encryption
    setItem: function(key, value, encrypt = false) {
      if (encrypt && value) {
        // Simple encryption for demo purposes
        // In production, use a proper encryption library
        value = btoa(JSON.stringify(value));
      }
      localStorage.setItem(key, value);
    },
    
    // Get item with optional decryption
    getItem: function(key, decrypt = false) {
      const value = localStorage.getItem(key);
      
      if (decrypt && value) {
        try {
          // Simple decryption for demo purposes
          return JSON.parse(atob(value));
        } catch (e) {
          console.error('Error decrypting value:', e);
          return null;
        }
      }
      
      return value;
    },
    
    // Remove item
    removeItem: function(key) {
      localStorage.removeItem(key);
    },
    
    // Clear all items
    clear: function() {
      localStorage.clear();
    }
  };
}

// Prevent clickjacking
if (window.self !== window.top) {
  // If the page is loaded in an iframe, redirect to the actual site
  window.top.location = window.self.location;
}

// Add security headers (in a real app, these would be set on the server)
// X-XSS-Protection: 1; mode=block
// X-Content-Type-Options: nosniff
// X-Frame-Options: DENY
// Referrer-Policy: strict-origin-when-cross-origin
// Feature-Policy: camera 'none'; microphone 'none'; geolocation 'self'
