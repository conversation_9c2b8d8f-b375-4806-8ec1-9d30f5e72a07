// Error handling tests for MindField

testFramework.test('Error Handler - Notification System', async () => {
  // Test success notification
  if (window.errorHandler) {
    window.errorHandler.showNotification('Test success message', 'success');
    
    await testFramework.wait(100);
    
    const notification = document.querySelector('.notification-success');
    testFramework.assertNotNull(notification, 'Success notification should appear');
    
    const message = notification.querySelector('.notification-message');
    testFramework.assertEqual(message.textContent, 'Test success message', 'Notification should show correct message');
    
    // Test notification auto-removal
    await testFramework.wait(5100); // Wait for auto-removal
    
    const removedNotification = document.querySelector('.notification-success');
    testFramework.assert(!removedNotification || removedNotification.parentElement === null, 'Notification should be auto-removed');
  } else {
    throw new Error('Error handler not available');
  }
});

testFramework.test('Error Handler - Different Notification Types', async () => {
  if (window.errorHandler) {
    // Test error notification
    window.errorHandler.showNotification('Test error message', 'error');
    await testFramework.wait(100);
    
    const errorNotification = document.querySelector('.notification-error');
    testFramework.assertNotNull(errorNotification, 'Error notification should appear');
    
    // Test warning notification
    window.errorHandler.showNotification('Test warning message', 'warning');
    await testFramework.wait(100);
    
    const warningNotification = document.querySelector('.notification-warning');
    testFramework.assertNotNull(warningNotification, 'Warning notification should appear');
    
    // Test info notification
    window.errorHandler.showNotification('Test info message', 'info');
    await testFramework.wait(100);
    
    const infoNotification = document.querySelector('.notification-info');
    testFramework.assertNotNull(infoNotification, 'Info notification should appear');
    
    // Cleanup notifications
    const notifications = document.querySelectorAll('.notification');
    notifications.forEach(n => n.remove());
  } else {
    throw new Error('Error handler not available');
  }
});

testFramework.test('Error Handler - Loading States', async () => {
  if (window.errorHandler) {
    // Create test element
    const testElement = testFramework.createElement('div', { id: 'test-loading' }, 'Test content');
    document.body.appendChild(testElement);
    
    // Show loading
    window.errorHandler.showLoading(testElement, 'Testing loading...');
    
    await testFramework.wait(100);
    
    const loadingOverlay = testElement.querySelector('.loading-overlay');
    testFramework.assertNotNull(loadingOverlay, 'Loading overlay should appear');
    
    const loadingMessage = testElement.querySelector('.loading-message');
    testFramework.assertEqual(loadingMessage.textContent, 'Testing loading...', 'Loading message should be correct');
    
    const spinner = testElement.querySelector('.spinner');
    testFramework.assertNotNull(spinner, 'Loading spinner should appear');
    
    // Hide loading
    window.errorHandler.hideLoading(testElement);
    
    await testFramework.wait(100);
    
    const removedOverlay = testElement.querySelector('.loading-overlay');
    testFramework.assert(!removedOverlay, 'Loading overlay should be removed');
    
    // Cleanup
    document.body.removeChild(testElement);
  } else {
    throw new Error('Error handler not available');
  }
});

testFramework.test('Error Handler - Form Validation', async () => {
  if (window.errorHandler) {
    const formData = {
      email: 'invalid-email',
      password: 'weak',
      name: ''
    };
    
    const rules = {
      email: { required: true, type: 'email', label: 'Email' },
      password: { required: true, type: 'password', minLength: 8, requireSpecial: true, requireNumber: true, label: 'Password' },
      name: { required: true, label: 'Name' }
    };
    
    const errors = window.errorHandler.validateForm(formData, rules);
    
    testFramework.assertTrue(errors.length > 0, 'Validation should return errors for invalid data');
    testFramework.assertContains(errors.join(' '), 'Name is required', 'Should validate required fields');
    testFramework.assertContains(errors.join(' '), 'valid Email', 'Should validate email format');
    testFramework.assertContains(errors.join(' '), 'at least 8 characters', 'Should validate password length');
  } else {
    throw new Error('Error handler not available');
  }
});

testFramework.test('Error Handler - Firebase Auth Error Messages', async () => {
  if (window.errorHandler) {
    const testCases = [
      { code: 'auth/user-not-found', expected: 'No account found with this email address.' },
      { code: 'auth/wrong-password', expected: 'Incorrect password. Please try again.' },
      { code: 'auth/email-already-in-use', expected: 'An account with this email already exists.' },
      { code: 'auth/weak-password', expected: 'Password should be at least 6 characters.' },
      { code: 'auth/invalid-email', expected: 'Please enter a valid email address.' },
      { code: 'unknown-error', expected: 'An error occurred. Please try again.' }
    ];
    
    testCases.forEach(testCase => {
      const message = window.errorHandler.getAuthErrorMessage(testCase.code);
      testFramework.assertEqual(message, testCase.expected, `Error code ${testCase.code} should return correct message`);
    });
  } else {
    throw new Error('Error handler not available');
  }
});

testFramework.test('Error Handler - Network Error Handling', async () => {
  if (window.errorHandler) {
    // Mock navigator.onLine
    const originalOnLine = navigator.onLine;
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: false
    });
    
    // Test offline handling
    window.errorHandler.handleNetworkError(new Error('Network error'));
    
    await testFramework.wait(100);
    
    const offlineNotification = document.querySelector('.notification-warning');
    testFramework.assertNotNull(offlineNotification, 'Offline notification should appear');
    
    // Restore navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: originalOnLine
    });
    
    // Cleanup
    if (offlineNotification) offlineNotification.remove();
  } else {
    throw new Error('Error handler not available');
  }
});
