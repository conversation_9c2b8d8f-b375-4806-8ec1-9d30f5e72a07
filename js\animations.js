// Animations JavaScript for MindField

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all animations
    initPageAnimations();

    // Add scroll animations
    addScrollAnimations();

    // Add hover animations
    addHoverAnimations();

    // Add click animations
    addClickAnimations();
});

// Initialize page load animations
function initPageAnimations() {
    // Animate elements that should be visible on page load
    const animatedElements = document.querySelectorAll('.animate-in');

    // Use setTimeout to ensure animations start after page load
    setTimeout(() => {
        animatedElements.forEach((element, index) => {
            // Add a small delay between each element
            setTimeout(() => {
                element.classList.add('visible');
            }, index * 100);
        });
    }, 100);
}

// Add scroll-triggered animations
function addScrollAnimations() {
    // Use Intersection Observer for scroll animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Add animation classes when element is in view
                entry.target.classList.add('visible');

                // Add additional animation classes if needed
                if (entry.target.classList.contains('add-float')) {
                    entry.target.classList.add('float');
                }

                if (entry.target.classList.contains('add-pulse')) {
                    entry.target.classList.add('pulse');
                }

                // Unobserve after animation is triggered
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    // Observe all animated elements
    document.querySelectorAll('.animate-in, .add-float, .add-pulse').forEach(element => {
        observer.observe(element);
    });

    // Add scroll indicator if it exists
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        // Hide scroll indicator when user scrolls down
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                scrollIndicator.style.opacity = '0';
            } else {
                scrollIndicator.style.opacity = '1';
            }
        });
    }
}

// Add hover animations
function addHoverAnimations() {
    // Add glow effect to cards on hover
    const cards = document.querySelectorAll('.group-card, .result-item');

    cards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.classList.add('glow');
        });

        card.addEventListener('mouseleave', () => {
            card.classList.remove('glow');
        });
    });

    // Add pulse effect to buttons on hover
    const buttons = document.querySelectorAll('.btn-primary');

    buttons.forEach(button => {
        button.addEventListener('mouseenter', () => {
            button.classList.add('pulse');
        });

        button.addEventListener('mouseleave', () => {
            setTimeout(() => {
                button.classList.remove('pulse');
            }, 500);
        });
    });
}

// Add click animations
function addClickAnimations() {
    // Add ripple effect to buttons
    const buttons = document.querySelectorAll('.btn');

    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple element
            const ripple = document.createElement('span');
            ripple.classList.add('ripple-effect');

            // Position the ripple
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);

            ripple.style.width = ripple.style.height = `${size}px`;
            ripple.style.left = `${e.clientX - rect.left - size/2}px`;
            ripple.style.top = `${e.clientY - rect.top - size/2}px`;

            // Add ripple to button
            button.appendChild(ripple);

            // Remove ripple after animation completes
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Add shake animation to form submit buttons
    const forms = document.querySelectorAll('form');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitButton = form.querySelector('button[type="submit"]');

            if (submitButton) {
                // Add success animation
                submitButton.classList.add('success-animation');
                submitButton.textContent = 'Success!';

                // Reset button after animation
                setTimeout(() => {
                    submitButton.classList.remove('success-animation');
                    submitButton.textContent = 'Save Changes';
                }, 2000);
            }

            // Show success message
            const successMessage = document.createElement('div');
            successMessage.classList.add('success-message', 'fade-in');
            successMessage.innerHTML = '<i class="fas fa-check-circle"></i> Changes saved successfully!';

            form.appendChild(successMessage);

            // Remove success message after delay
            setTimeout(() => {
                successMessage.classList.remove('fade-in');
                successMessage.classList.add('fade-out');

                setTimeout(() => {
                    successMessage.remove();
                }, 500);
            }, 3000);
        });
    });

    // Add bounce animation to profile navigation items
    const navItems = document.querySelectorAll('.profile-nav-item');

    navItems.forEach(item => {
        item.addEventListener('click', function() {
            this.classList.add('bounce');

            setTimeout(() => {
                this.classList.remove('bounce');
            }, 1000);
        });
    });
}

// Add micro-interactions to specific elements
document.addEventListener('DOMContentLoaded', function() {
    // Logo animation removed - logo now functions as a simple home button without animations

    // Hamburger menu animation
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('.nav-links');

    if (hamburger && navLinks) {
        hamburger.addEventListener('click', function() {
            this.classList.toggle('active');

            if (this.classList.contains('active')) {
                navLinks.classList.add('slide-in-right');
            } else {
                navLinks.classList.remove('slide-in-right');
                navLinks.classList.add('slide-out-right');

                setTimeout(() => {
                    navLinks.classList.remove('slide-out-right');
                }, 500);
            }
        });
    }

    // Add typing animation to headlines if they have the class
    const typingElements = document.querySelectorAll('.typing');

    typingElements.forEach(element => {
        // Store original text
        const originalText = element.textContent;

        // Clear text
        element.textContent = '';

        // Add typing animation
        let i = 0;
        const typingInterval = setInterval(() => {
            if (i < originalText.length) {
                element.textContent += originalText.charAt(i);
                i++;
            } else {
                clearInterval(typingInterval);
            }
        }, 100);
    });
});
