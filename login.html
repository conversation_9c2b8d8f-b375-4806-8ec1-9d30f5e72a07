<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindField - Login</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="login-page">
    <header>
        <nav class="navbar">
            <div class="logo"><a href="index.html">MindField</a></div>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="about.html">About</a>
                <a href="groups.html">Groups</a>
                <a href="browse.html">Browse</a>
                <a href="contact.html">Contact</a>
            </div>
            <div class="profile-icon active">
                <a href="login.html"><i class="fas fa-user-circle"></i></a>
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <main class="login-container">
        <div class="auth-card animate-in">
            <div class="auth-tabs">
                <button class="auth-tab active" data-tab="login">Login</button>
                <button class="auth-tab" data-tab="signup">Sign Up</button>
            </div>

            <div id="login-form" class="auth-form active">
                <h2>Welcome Back</h2>
                <div id="login-error" class="auth-error" style="display: none;"></div>
                <div class="form-group">
                    <label for="login-email">Email</label>
                    <input type="email" id="login-email" required>
                </div>
                <div class="form-group">
                    <label for="login-password">Password</label>
                    <input type="password" id="login-password" required>
                </div>
                <div class="form-actions">
                    <button type="button" id="login-button" class="btn primary-btn">Login</button>
                </div>
                <div class="auth-options">
                    <a href="#" id="forgot-password">Forgot Password?</a>
                </div>
                <div class="social-auth">
                    <button type="button" id="google-login" class="btn btn-outline">
                        <i class="fab fa-google"></i> Continue with Google
                    </button>
                </div>
            </div>

            <div id="signup-form" class="auth-form">
                <h2>Create Account</h2>
                <div id="signup-error" class="auth-error" style="display: none;"></div>
                <div class="form-group">
                    <label for="signup-name">Full Name</label>
                    <input type="text" id="signup-name" required>
                </div>
                <div class="form-group">
                    <label for="signup-email">Email</label>
                    <input type="email" id="signup-email" required>
                </div>
                <div class="form-group">
                    <label for="signup-password">Password</label>
                    <input type="password" id="signup-password" required>
                    <small>Password must be at least 8 characters with a number and special character</small>
                </div>
                <div class="form-group">
                    <label for="signup-confirm">Confirm Password</label>
                    <input type="password" id="signup-confirm" required>
                </div>
                <div class="form-actions">
                    <button type="button" id="signup-button" class="btn primary-btn">Sign Up</button>
                </div>
                <div class="social-auth">
                    <button type="button" id="google-signup" class="btn btn-outline">
                        <i class="fab fa-google"></i> Sign Up with Google
                    </button>
                </div>
            </div>

            <div id="reset-form" class="auth-form">
                <h2>Reset Password</h2>
                <div id="reset-message" class="auth-message" style="display: none;"></div>
                <p>Enter your email address and we'll send you a link to reset your password.</p>
                <div class="form-group">
                    <label for="reset-email">Email</label>
                    <input type="email" id="reset-email" required>
                </div>
                <div class="form-actions">
                    <button type="button" id="reset-button" class="btn primary-btn">Send Reset Link</button>
                    <button type="button" id="back-to-login" class="btn btn-outline">Back to Login</button>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <div class="logo"><a href="index.html">MindField</a></div>
                <p>Transform Your Life</p>
            </div>
            <div class="footer-links">
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="about.html">About Us</a>
                    <a href="#">Careers</a>
                    <a href="#">Press</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Blog</a>
                    <a href="#">Community</a>
                    <a href="#">Guides</a>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <a href="#">Help Center</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 MindField. All rights reserved.</p>
            <div class="social-icons">
                <a href="#"><i class="fab fa-facebook"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-storage.js"></script>

    <!-- App Scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/security.js"></script>
    <script src="js/performance.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/database.js"></script>
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/login.js"></script>
    <script src="js/simple-test-user.js"></script>
</body>
</html>
