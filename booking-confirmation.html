<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindField - Booking Confirmation</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="confirmation-page">
    <header>
        <nav class="navbar">
            <div class="logo"><a href="index.html">MindField</a></div>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="about.html">About</a>
                <a href="groups.html">Groups</a>
                <a href="browse.html">Browse</a>
                <a href="index.html#contact">Contact</a>
            </div>
            <div class="profile-icon">
                <a href="login.html"><i class="fas fa-user-circle"></i></a>
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <main class="confirmation-container">
        <div class="confirmation-card animate-in">
            <div class="confirmation-header">
                <i class="fas fa-check-circle"></i>
                <h1>Booking Confirmed!</h1>
                <p>Your retreat has been successfully booked.</p>
            </div>

            <div class="confirmation-details">
                <div class="confirmation-section">
                    <h2>Booking Details</h2>
                    <div class="detail-item">
                        <span class="detail-label">Booking ID:</span>
                        <span class="detail-value" id="booking-id">BK12345</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Date:</span>
                        <span class="detail-value" id="booking-date">June 15, 2023</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value status-confirmed" id="booking-status">Confirmed</span>
                    </div>
                </div>

                <div class="confirmation-section">
                    <h2>Retreat Information</h2>
                    <div class="retreat-info">
                        <div class="retreat-image">
                            <img src="https://via.placeholder.com/150x150" alt="Retreat" id="retreat-image">
                        </div>
                        <div class="retreat-details">
                            <h3 id="retreat-title">Mindfulness in the Mountains</h3>
                            <p><i class="fas fa-map-marker-alt"></i> <span id="retreat-location">Bali, Indonesia</span></p>
                            <p><i class="fas fa-calendar"></i> <span id="retreat-dates">June 15-22, 2023</span></p>
                            <p><i class="fas fa-user"></i> <span id="retreat-guide">Led by Sarah Johnson</span></p>
                        </div>
                    </div>
                </div>

                <div class="confirmation-section">
                    <h2>Payment Summary</h2>
                    <div class="detail-item">
                        <span class="detail-label">Retreat Cost:</span>
                        <span class="detail-value" id="retreat-cost">$1,200.00</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Taxes & Fees:</span>
                        <span class="detail-value" id="retreat-fees">$120.00</span>
                    </div>
                    <div class="detail-item total">
                        <span class="detail-label">Total Paid:</span>
                        <span class="detail-value" id="retreat-total">$1,320.00</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Payment Method:</span>
                        <span class="detail-value" id="payment-method">Visa ending in 4242</span>
                    </div>
                </div>
            </div>

            <div class="confirmation-actions">
                <a href="profile.html?tab=bookings" class="btn btn-primary">View My Bookings</a>
                <a href="browse.html" class="btn btn-outline">Explore More Retreats</a>
            </div>

            <div class="confirmation-help">
                <p>Need help with your booking? <a href="#">Contact Support</a></p>
            </div>
        </div>

        <div class="next-steps">
            <h2>What's Next?</h2>
            <div class="steps-container">
                <div class="step-item animate-in">
                    <div class="step-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="step-content">
                        <h3>Check Your Email</h3>
                        <p>We've sent a confirmation email with all the details of your booking.</p>
                    </div>
                </div>

                <div class="step-item animate-in">
                    <div class="step-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="step-content">
                        <h3>Mark Your Calendar</h3>
                        <p>Add this retreat to your calendar so you don't miss this transformative experience.</p>
                    </div>
                </div>

                <div class="step-item animate-in">
                    <div class="step-icon">
                        <i class="fas fa-suitcase"></i>
                    </div>
                    <div class="step-content">
                        <h3>Prepare for Your Journey</h3>
                        <p>Check out our <a href="#">preparation guide</a> to make the most of your retreat experience.</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <div class="logo"><a href="index.html">MindField</a></div>
                <p>Transform Your Life</p>
            </div>
            <div class="footer-links">
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="#">About Us</a>
                    <a href="#">Careers</a>
                    <a href="#">Press</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Blog</a>
                    <a href="#">Community</a>
                    <a href="#">Guides</a>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <a href="#">Help Center</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 MindField. All rights reserved.</p>
            <div class="social-icons">
                <a href="#"><i class="fab fa-facebook"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-storage.js"></script>

    <!-- Stripe SDK -->
    <script src="https://js.stripe.com/v3/"></script>

    <!-- App Scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/security.js"></script>
    <script src="js/performance.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/database.js"></script>
    <script src="js/payment.js"></script>
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/simple-test-user.js"></script>

    <!-- Confirmation Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get booking ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const bookingId = urlParams.get('id');

            if (bookingId) {
                // In a real app, this would fetch the booking details from the database
                // For demo purposes, we'll use mock data
                loadBookingDetails(bookingId);
            } else {
                // Redirect to profile if no booking ID
                window.location.href = 'profile.html?tab=bookings';
            }
        });

        function loadBookingDetails(bookingId) {
            // In a real app, this would fetch the booking from Firestore
            // For demo purposes, we'll use mock data

            // Update booking ID
            document.getElementById('booking-id').textContent = bookingId || 'BK12345';

            // Update booking date
            const bookingDate = new Date();
            document.getElementById('booking-date').textContent = bookingDate.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            // Add calendar download link
            const calendarLink = document.createElement('a');
            calendarLink.href = '#';
            calendarLink.className = 'btn-text';
            calendarLink.innerHTML = '<i class="fas fa-calendar-plus"></i> Add to Calendar';
            calendarLink.addEventListener('click', function(e) {
                e.preventDefault();
                addToCalendar();
            });

            document.querySelector('.retreat-details').appendChild(calendarLink);
        }

        function addToCalendar() {
            // In a real app, this would generate a calendar file
            // For demo purposes, we'll show an alert
            alert('Calendar event added successfully!');
        }
    </script>
</body>
</html>
