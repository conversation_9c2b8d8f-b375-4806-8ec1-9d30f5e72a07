// Simple testing framework for MindField
// Lightweight testing suite for client-side testing

class TestFramework {
  constructor() {
    this.tests = [];
    this.results = {
      passed: 0,
      failed: 0,
      total: 0
    };
    this.setupTestEnvironment();
  }

  // Setup test environment
  setupTestEnvironment() {
    // Create test container
    const testContainer = document.createElement('div');
    testContainer.id = 'test-container';
    testContainer.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 400px;
      max-height: 500px;
      background: white;
      border: 2px solid #6c63ff;
      border-radius: 8px;
      padding: 1rem;
      z-index: 10000;
      overflow-y: auto;
      font-family: monospace;
      font-size: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    `;
    
    // Add toggle button
    const toggleButton = document.createElement('button');
    toggleButton.textContent = 'Toggle Tests';
    toggleButton.style.cssText = `
      position: fixed;
      top: 10px;
      right: 420px;
      background: #6c63ff;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      z-index: 10001;
      font-size: 12px;
    `;
    
    toggleButton.addEventListener('click', () => {
      testContainer.style.display = testContainer.style.display === 'none' ? 'block' : 'none';
    });
    
    document.body.appendChild(testContainer);
    document.body.appendChild(toggleButton);
    
    this.testContainer = testContainer;
    this.updateDisplay();
  }

  // Add a test
  test(name, testFunction) {
    this.tests.push({
      name,
      testFunction,
      status: 'pending'
    });
  }

  // Run all tests
  async runTests() {
    console.log('🧪 Starting MindField Test Suite...');
    this.results = { passed: 0, failed: 0, total: this.tests.length };
    
    for (const test of this.tests) {
      try {
        await test.testFunction();
        test.status = 'passed';
        test.message = '✅ Passed';
        this.results.passed++;
        console.log(`✅ ${test.name}`);
      } catch (error) {
        test.status = 'failed';
        test.message = `❌ ${error.message}`;
        this.results.failed++;
        console.error(`❌ ${test.name}: ${error.message}`);
      }
    }
    
    this.updateDisplay();
    this.logSummary();
  }

  // Update test display
  updateDisplay() {
    if (!this.testContainer) return;
    
    const html = `
      <div style="margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 1px solid #eee;">
        <h3 style="margin: 0; color: #6c63ff;">MindField Tests</h3>
        <div style="margin-top: 0.5rem;">
          <span style="color: green;">✅ ${this.results.passed}</span> |
          <span style="color: red;">❌ ${this.results.failed}</span> |
          <span>Total: ${this.results.total}</span>
        </div>
      </div>
      <div style="max-height: 300px; overflow-y: auto;">
        ${this.tests.map(test => `
          <div style="margin-bottom: 0.5rem; padding: 0.25rem; background: ${test.status === 'passed' ? '#f0f8f0' : test.status === 'failed' ? '#fff0f0' : '#f8f8f8'}; border-radius: 4px;">
            <div style="font-weight: bold; color: ${test.status === 'passed' ? 'green' : test.status === 'failed' ? 'red' : 'gray'};">
              ${test.name}
            </div>
            <div style="font-size: 10px; color: #666;">
              ${test.message || 'Pending...'}
            </div>
          </div>
        `).join('')}
      </div>
      <button onclick="testFramework.runTests()" style="margin-top: 1rem; background: #6c63ff; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; width: 100%;">
        Run Tests
      </button>
    `;
    
    this.testContainer.innerHTML = html;
  }

  // Log test summary
  logSummary() {
    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`📈 Success Rate: ${((this.results.passed / this.results.total) * 100).toFixed(1)}%`);
  }

  // Assertion helpers
  assert(condition, message = 'Assertion failed') {
    if (!condition) {
      throw new Error(message);
    }
  }

  assertEqual(actual, expected, message = `Expected ${expected}, got ${actual}`) {
    if (actual !== expected) {
      throw new Error(message);
    }
  }

  assertNotNull(value, message = 'Value should not be null') {
    if (value === null || value === undefined) {
      throw new Error(message);
    }
  }

  assertTrue(condition, message = 'Expected true') {
    if (!condition) {
      throw new Error(message);
    }
  }

  assertFalse(condition, message = 'Expected false') {
    if (condition) {
      throw new Error(message);
    }
  }

  assertContains(container, item, message = `Container should contain ${item}`) {
    if (!container.includes(item)) {
      throw new Error(message);
    }
  }

  // DOM testing helpers
  createElement(tag, attributes = {}, content = '') {
    const element = document.createElement(tag);
    Object.keys(attributes).forEach(key => {
      element.setAttribute(key, attributes[key]);
    });
    if (content) {
      element.innerHTML = content;
    }
    return element;
  }

  // Simulate user events
  simulateClick(element) {
    const event = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    element.dispatchEvent(event);
  }

  simulateKeyPress(element, key) {
    const event = new KeyboardEvent('keydown', {
      key: key,
      bubbles: true,
      cancelable: true
    });
    element.dispatchEvent(event);
  }

  simulateInput(element, value) {
    element.value = value;
    const event = new Event('input', {
      bubbles: true,
      cancelable: true
    });
    element.dispatchEvent(event);
  }

  // Wait for async operations
  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Wait for element to appear
  async waitForElement(selector, timeout = 5000) {
    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
      const element = document.querySelector(selector);
      if (element) {
        return element;
      }
      await this.wait(100);
    }
    throw new Error(`Element ${selector} not found within ${timeout}ms`);
  }
}

// Initialize test framework
const testFramework = new TestFramework();
window.testFramework = testFramework;
