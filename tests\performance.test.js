// Performance tests for MindField

testFramework.test('Performance - Debounce Function', async () => {
  if (window.debounce) {
    let callCount = 0;
    const testFunction = () => { callCount++; };
    const debouncedFunction = window.debounce(testFunction, 100);
    
    // Call function multiple times rapidly
    debouncedFunction();
    debouncedFunction();
    debouncedFunction();
    debouncedFunction();
    
    // Should not have been called yet
    testFramework.assertEqual(callCount, 0, 'Debounced function should not be called immediately');
    
    // Wait for debounce delay
    await testFramework.wait(150);
    
    // Should have been called only once
    testFramework.assertEqual(callCount, 1, 'Debounced function should be called only once after delay');
  } else {
    throw new Error('Debounce function not available');
  }
});

testFramework.test('Performance - Element Caching', async () => {
  if (window.getElement) {
    // Create test element
    const testElement = testFramework.createElement('div', { id: 'test-cache-element' });
    document.body.appendChild(testElement);
    
    // First call should query and cache
    const element1 = window.getElement('#test-cache-element');
    testFramework.assertEqual(element1, testElement, 'Should return correct element');
    
    // Second call should return cached element
    const element2 = window.getElement('#test-cache-element');
    testFramework.assertEqual(element2, testElement, 'Should return same cached element');
    testFramework.assertEqual(element1, element2, 'Cached element should be identical');
    
    // Cleanup
    document.body.removeChild(testElement);
  } else {
    throw new Error('Element caching not available');
  }
});

testFramework.test('Performance - Local Caching System', async () => {
  if (window.dataCache) {
    const testKey = 'test-cache-key';
    const testValue = { data: 'test data', timestamp: Date.now() };
    
    // Test setting cache item
    window.dataCache.setItem(testKey, testValue, 1); // 1 minute expiration
    
    // Test getting cache item
    const cachedValue = window.dataCache.getItem(testKey);
    testFramework.assertNotNull(cachedValue, 'Cached value should be retrievable');
    testFramework.assertEqual(cachedValue.data, testValue.data, 'Cached value should match original');
    
    // Test cache expiration
    window.dataCache.setItem('expired-key', 'expired-value', -1); // Already expired
    const expiredValue = window.dataCache.getItem('expired-key');
    testFramework.assert(expiredValue === null, 'Expired cache item should return null');
    
    // Test cache removal
    window.dataCache.removeItem(testKey);
    const removedValue = window.dataCache.getItem(testKey);
    testFramework.assert(removedValue === null, 'Removed cache item should return null');
    
    // Test cache clearing
    window.dataCache.setItem('clear-test-1', 'value1');
    window.dataCache.setItem('clear-test-2', 'value2');
    window.dataCache.clear();
    
    const clearedValue1 = window.dataCache.getItem('clear-test-1');
    const clearedValue2 = window.dataCache.getItem('clear-test-2');
    testFramework.assert(clearedValue1 === null && clearedValue2 === null, 'All cache items should be cleared');
  } else {
    throw new Error('Data cache not available');
  }
});

testFramework.test('Performance - Lazy Loading Setup', async () => {
  // Create test image with data-src attribute
  const testImage = testFramework.createElement('img', {
    'data-src': 'https://via.placeholder.com/300x200',
    alt: 'Test image'
  });
  document.body.appendChild(testImage);
  
  // Check that image doesn't have src initially
  testFramework.assert(!testImage.src || testImage.src === '', 'Image should not have src initially');
  testFramework.assertNotNull(testImage.getAttribute('data-src'), 'Image should have data-src attribute');
  
  // Simulate intersection (image coming into view)
  // Note: This is a simplified test - real lazy loading would use IntersectionObserver
  if (window.IntersectionObserver) {
    testFramework.assertTrue(true, 'IntersectionObserver is supported for lazy loading');
  } else {
    testFramework.assertTrue(true, 'Fallback lazy loading should be available');
  }
  
  // Cleanup
  document.body.removeChild(testImage);
});

testFramework.test('Performance - Event Delegation', async () => {
  // Create container with multiple buttons
  const container = testFramework.createElement('div', { class: 'test-container' }, `
    <button class="btn test-btn-1">Button 1</button>
    <button class="btn test-btn-2">Button 2</button>
    <button class="btn test-btn-3">Button 3</button>
  `);
  document.body.appendChild(container);
  
  let clickCount = 0;
  
  // Add event listener to container (event delegation)
  container.addEventListener('click', (e) => {
    if (e.target.classList.contains('btn')) {
      clickCount++;
    }
  });
  
  // Test clicking different buttons
  const btn1 = container.querySelector('.test-btn-1');
  const btn2 = container.querySelector('.test-btn-2');
  const btn3 = container.querySelector('.test-btn-3');
  
  testFramework.simulateClick(btn1);
  testFramework.simulateClick(btn2);
  testFramework.simulateClick(btn3);
  
  await testFramework.wait(100);
  
  testFramework.assertEqual(clickCount, 3, 'Event delegation should handle all button clicks');
  
  // Cleanup
  document.body.removeChild(container);
});

testFramework.test('Performance - Virtual Scrolling Detection', async () => {
  // Create a large list to test virtual scrolling
  const resultsList = testFramework.createElement('div', { id: 'test-results-list' });
  
  // Add many items to trigger virtual scrolling
  for (let i = 0; i < 25; i++) {
    const item = testFramework.createElement('div', { class: 'result-item' }, `Item ${i}`);
    resultsList.appendChild(item);
  }
  
  document.body.appendChild(resultsList);
  
  // Check if virtual scrolling would be triggered (>20 items)
  const itemCount = resultsList.children.length;
  testFramework.assertTrue(itemCount > 20, 'List should have enough items to trigger virtual scrolling');
  
  // In a real implementation, virtual scrolling would create placeholder and visible containers
  // This test just verifies the conditions are met
  
  // Cleanup
  document.body.removeChild(resultsList);
});

testFramework.test('Performance - Animation Performance', async () => {
  // Test that animations can be disabled for performance
  const testElement = testFramework.createElement('div', { 
    class: 'test-animation',
    style: 'transition: transform 0.3s ease;'
  });
  document.body.appendChild(testElement);
  
  // Test reduced motion preference
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
  
  if (prefersReducedMotion.matches) {
    // If user prefers reduced motion, animations should be minimal
    testFramework.assertTrue(true, 'Reduced motion preference detected and should be respected');
  } else {
    // Test that animations are available
    const computedStyle = window.getComputedStyle(testElement);
    const transition = computedStyle.getPropertyValue('transition');
    testFramework.assertTrue(transition.includes('transform'), 'Animations should be available when not reduced');
  }
  
  // Cleanup
  document.body.removeChild(testElement);
});

testFramework.test('Performance - Memory Leak Prevention', async () => {
  // Test that event listeners are properly managed
  let listenerCount = 0;
  const originalAddEventListener = Element.prototype.addEventListener;
  const originalRemoveEventListener = Element.prototype.removeEventListener;
  
  // Mock addEventListener to count listeners
  Element.prototype.addEventListener = function(...args) {
    listenerCount++;
    return originalAddEventListener.apply(this, args);
  };
  
  Element.prototype.removeEventListener = function(...args) {
    listenerCount--;
    return originalRemoveEventListener.apply(this, args);
  };
  
  // Create and remove elements with listeners
  const testElement = testFramework.createElement('button', {}, 'Test Button');
  document.body.appendChild(testElement);
  
  const testHandler = () => {};
  testElement.addEventListener('click', testHandler);
  
  const initialCount = listenerCount;
  
  testElement.removeEventListener('click', testHandler);
  document.body.removeChild(testElement);
  
  // Restore original methods
  Element.prototype.addEventListener = originalAddEventListener;
  Element.prototype.removeEventListener = originalRemoveEventListener;
  
  testFramework.assertTrue(listenerCount < initialCount, 'Event listeners should be properly cleaned up');
});

testFramework.test('Performance - Resource Loading Optimization', async () => {
  // Test that critical resources are identified
  const criticalImages = [];
  const criticalCSS = [];
  const criticalJS = [];
  
  // In a real implementation, these would be populated by the performance module
  testFramework.assertTrue(Array.isArray(criticalImages), 'Critical images array should exist');
  testFramework.assertTrue(Array.isArray(criticalCSS), 'Critical CSS array should exist');
  testFramework.assertTrue(Array.isArray(criticalJS), 'Critical JS array should exist');
  
  // Test preload link creation
  const preloadLink = testFramework.createElement('link', {
    rel: 'preload',
    as: 'style',
    href: 'test.css'
  });
  
  testFramework.assertEqual(preloadLink.rel, 'preload', 'Preload link should have correct rel attribute');
  testFramework.assertEqual(preloadLink.as, 'style', 'Preload link should have correct as attribute');
});
