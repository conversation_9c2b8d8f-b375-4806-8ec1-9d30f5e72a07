// Database functionality for MindField

// Collection references
const usersRef = db.collection('users');
const retreatsRef = db.collection('retreats');
const guidesRef = db.collection('guides');
const spacesRef = db.collection('spaces');
const bookingsRef = db.collection('bookings');
const reviewsRef = db.collection('reviews');

// Get retreats with pagination
function getRetreats(filters = {}, lastDoc = null, limit = 10) {
  let query = retreatsRef;
  
  // Apply filters
  if (filters.location) {
    query = query.where('location', '==', filters.location);
  }
  
  if (filters.minPrice && filters.maxPrice) {
    query = query.where('price', '>=', filters.minPrice)
                .where('price', '<=', filters.maxPrice);
  } else if (filters.minPrice) {
    query = query.where('price', '>=', filters.minPrice);
  } else if (filters.maxPrice) {
    query = query.where('price', '<=', filters.maxPrice);
  }
  
  if (filters.startDate) {
    query = query.where('startDate', '>=', filters.startDate);
  }
  
  if (filters.endDate) {
    query = query.where('endDate', '<=', filters.endDate);
  }
  
  if (filters.type) {
    query = query.where('type', '==', filters.type);
  }
  
  // Apply pagination
  if (lastDoc) {
    query = query.startAfter(lastDoc);
  }
  
  // Apply limit and order
  query = query.orderBy('createdAt', 'desc').limit(limit);
  
  return query.get()
    .then(snapshot => {
      const retreats = [];
      snapshot.forEach(doc => {
        retreats.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      // Get the last document for pagination
      const lastVisible = snapshot.docs[snapshot.docs.length - 1];
      
      return {
        retreats,
        lastDoc: lastVisible
      };
    });
}

// Get guides with pagination
function getGuides(filters = {}, lastDoc = null, limit = 10) {
  let query = guidesRef;
  
  // Apply filters
  if (filters.location) {
    query = query.where('location', '==', filters.location);
  }
  
  if (filters.specialties && filters.specialties.length > 0) {
    query = query.where('specialties', 'array-contains-any', filters.specialties);
  }
  
  // Apply pagination
  if (lastDoc) {
    query = query.startAfter(lastDoc);
  }
  
  // Apply limit and order
  query = query.orderBy('rating', 'desc').limit(limit);
  
  return query.get()
    .then(snapshot => {
      const guides = [];
      snapshot.forEach(doc => {
        guides.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      // Get the last document for pagination
      const lastVisible = snapshot.docs[snapshot.docs.length - 1];
      
      return {
        guides,
        lastDoc: lastVisible
      };
    });
}

// Get spaces with pagination
function getSpaces(filters = {}, lastDoc = null, limit = 10) {
  let query = spacesRef;
  
  // Apply filters
  if (filters.location) {
    query = query.where('location', '==', filters.location);
  }
  
  if (filters.minPrice && filters.maxPrice) {
    query = query.where('price', '>=', filters.minPrice)
                .where('price', '<=', filters.maxPrice);
  } else if (filters.minPrice) {
    query = query.where('price', '>=', filters.minPrice);
  } else if (filters.maxPrice) {
    query = query.where('price', '<=', filters.maxPrice);
  }
  
  if (filters.capacity) {
    query = query.where('capacity', '>=', filters.capacity);
  }
  
  if (filters.amenities && filters.amenities.length > 0) {
    query = query.where('amenities', 'array-contains-any', filters.amenities);
  }
  
  // Apply pagination
  if (lastDoc) {
    query = query.startAfter(lastDoc);
  }
  
  // Apply limit and order
  query = query.orderBy('createdAt', 'desc').limit(limit);
  
  return query.get()
    .then(snapshot => {
      const spaces = [];
      snapshot.forEach(doc => {
        spaces.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      // Get the last document for pagination
      const lastVisible = snapshot.docs[snapshot.docs.length - 1];
      
      return {
        spaces,
        lastDoc: lastVisible
      };
    });
}

// Get user bookings
function getUserBookings(userId) {
  return bookingsRef.where('userId', '==', userId)
    .orderBy('createdAt', 'desc')
    .get()
    .then(snapshot => {
      const bookings = [];
      snapshot.forEach(doc => {
        bookings.push({
          id: doc.id,
          ...doc.data()
        });
      });
      return bookings;
    });
}

// Get user listings (retreats, guides, spaces)
function getUserListings(userId) {
  const promises = [
    retreatsRef.where('hostId', '==', userId).get(),
    guidesRef.where('userId', '==', userId).get(),
    spacesRef.where('ownerId', '==', userId).get()
  ];
  
  return Promise.all(promises)
    .then(([retreatsSnapshot, guidesSnapshot, spacesSnapshot]) => {
      const listings = {
        retreats: [],
        guides: [],
        spaces: []
      };
      
      retreatsSnapshot.forEach(doc => {
        listings.retreats.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      guidesSnapshot.forEach(doc => {
        listings.guides.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      spacesSnapshot.forEach(doc => {
        listings.spaces.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      return listings;
    });
}

// Create a new retreat
function createRetreat(retreatData) {
  // Add timestamp
  retreatData.createdAt = firebase.firestore.FieldValue.serverTimestamp();
  
  return retreatsRef.add(retreatData)
    .then(docRef => {
      return {
        id: docRef.id,
        ...retreatData
      };
    });
}

// Create a new guide profile
function createGuideProfile(guideData) {
  // Add timestamp
  guideData.createdAt = firebase.firestore.FieldValue.serverTimestamp();
  
  return guidesRef.add(guideData)
    .then(docRef => {
      return {
        id: docRef.id,
        ...guideData
      };
    });
}

// Create a new space listing
function createSpace(spaceData) {
  // Add timestamp
  spaceData.createdAt = firebase.firestore.FieldValue.serverTimestamp();
  
  return spacesRef.add(spaceData)
    .then(docRef => {
      return {
        id: docRef.id,
        ...spaceData
      };
    });
}

// Create a new booking
function createBooking(bookingData) {
  // Add timestamp
  bookingData.createdAt = firebase.firestore.FieldValue.serverTimestamp();
  bookingData.status = 'pending';
  
  return bookingsRef.add(bookingData)
    .then(docRef => {
      return {
        id: docRef.id,
        ...bookingData
      };
    });
}

// Update booking status
function updateBookingStatus(bookingId, status) {
  return bookingsRef.doc(bookingId).update({
    status: status,
    updatedAt: firebase.firestore.FieldValue.serverTimestamp()
  });
}

// Create a review
function createReview(reviewData) {
  // Add timestamp
  reviewData.createdAt = firebase.firestore.FieldValue.serverTimestamp();
  
  return reviewsRef.add(reviewData)
    .then(docRef => {
      // Update average rating on the target (retreat, guide, or space)
      return updateAverageRating(reviewData.targetType, reviewData.targetId)
        .then(() => {
          return {
            id: docRef.id,
            ...reviewData
          };
        });
    });
}

// Update average rating
function updateAverageRating(targetType, targetId) {
  return reviewsRef.where('targetType', '==', targetType)
    .where('targetId', '==', targetId)
    .get()
    .then(snapshot => {
      let totalRating = 0;
      let count = 0;
      
      snapshot.forEach(doc => {
        totalRating += doc.data().rating;
        count++;
      });
      
      const averageRating = count > 0 ? totalRating / count : 0;
      
      // Update the target with new average rating
      let targetRef;
      
      switch (targetType) {
        case 'retreat':
          targetRef = retreatsRef.doc(targetId);
          break;
        case 'guide':
          targetRef = guidesRef.doc(targetId);
          break;
        case 'space':
          targetRef = spacesRef.doc(targetId);
          break;
      }
      
      if (targetRef) {
        return targetRef.update({
          rating: averageRating,
          reviewCount: count
        });
      }
      
      return Promise.resolve();
    });
}

// Update user profile
function updateUserProfile(userId, profileData) {
  return usersRef.doc(userId).update({
    ...profileData,
    updatedAt: firebase.firestore.FieldValue.serverTimestamp()
  });
}

// Get retreat by ID
function getRetreatById(retreatId) {
  return retreatsRef.doc(retreatId).get()
    .then(doc => {
      if (doc.exists) {
        return {
          id: doc.id,
          ...doc.data()
        };
      } else {
        throw new Error('Retreat not found');
      }
    });
}

// Get guide by ID
function getGuideById(guideId) {
  return guidesRef.doc(guideId).get()
    .then(doc => {
      if (doc.exists) {
        return {
          id: doc.id,
          ...doc.data()
        };
      } else {
        throw new Error('Guide not found');
      }
    });
}

// Get space by ID
function getSpaceById(spaceId) {
  return spacesRef.doc(spaceId).get()
    .then(doc => {
      if (doc.exists) {
        return {
          id: doc.id,
          ...doc.data()
        };
      } else {
        throw new Error('Space not found');
      }
    });
}

// Get reviews for a target
function getReviews(targetType, targetId) {
  return reviewsRef.where('targetType', '==', targetType)
    .where('targetId', '==', targetId)
    .orderBy('createdAt', 'desc')
    .get()
    .then(snapshot => {
      const reviews = [];
      snapshot.forEach(doc => {
        reviews.push({
          id: doc.id,
          ...doc.data()
        });
      });
      return reviews;
    });
}

// Search retreats, guides, and spaces
function search(query, type = 'all', limit = 10) {
  // For a production app, you would use Algolia or Firebase Extensions for search
  // This is a simple implementation that searches by title and description
  
  const promises = [];
  
  if (type === 'all' || type === 'retreats') {
    promises.push(
      retreatsRef.orderBy('title').startAt(query).endAt(query + '\uf8ff').limit(limit).get()
        .then(snapshot => {
          const results = [];
          snapshot.forEach(doc => {
            results.push({
              id: doc.id,
              type: 'retreat',
              ...doc.data()
            });
          });
          return results;
        })
    );
  }
  
  if (type === 'all' || type === 'guides') {
    promises.push(
      guidesRef.orderBy('name').startAt(query).endAt(query + '\uf8ff').limit(limit).get()
        .then(snapshot => {
          const results = [];
          snapshot.forEach(doc => {
            results.push({
              id: doc.id,
              type: 'guide',
              ...doc.data()
            });
          });
          return results;
        })
    );
  }
  
  if (type === 'all' || type === 'spaces') {
    promises.push(
      spacesRef.orderBy('title').startAt(query).endAt(query + '\uf8ff').limit(limit).get()
        .then(snapshot => {
          const results = [];
          snapshot.forEach(doc => {
            results.push({
              id: doc.id,
              type: 'space',
              ...doc.data()
            });
          });
          return results;
        })
    );
  }
  
  return Promise.all(promises)
    .then(resultsArray => {
      // Flatten and sort results
      return [].concat(...resultsArray)
        .sort((a, b) => b.rating - a.rating);
    });
}

// Export database functions
const dbService = {
  getRetreats,
  getGuides,
  getSpaces,
  getUserBookings,
  getUserListings,
  createRetreat,
  createGuideProfile,
  createSpace,
  createBooking,
  updateBookingStatus,
  createReview,
  updateUserProfile,
  getRetreatById,
  getGuideById,
  getSpaceById,
  getReviews,
  search
};
