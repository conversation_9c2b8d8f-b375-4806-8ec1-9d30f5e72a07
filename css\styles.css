/* Enhanced Design System */
:root {
    /* Color Palette */
    --primary-color: #6c63ff;
    --primary-light: #8a84ff;
    --primary-dark: #5046e5;
    --primary-gradient: linear-gradient(135deg, #6c63ff 0%, #8a84ff 100%);
    --secondary-color: #63d1ff;
    --secondary-gradient: linear-gradient(135deg, #63d1ff 0%, #4bb5e6 100%);
    --accent-color: #ff6584;
    --accent-gradient: linear-gradient(135deg, #ff6584 0%, #ff8a9b 100%);

    /* Text Colors */
    --text-color: #2d3748;
    --text-light: #718096;
    --text-lighter: #a0aec0;
    --text-white: #ffffff;

    /* Background Colors */
    --background-color: #ffffff;
    --background-light: #f7fafc;
    --background-lighter: #edf2f7;
    --background-dark: #e2e8f0;
    --surface-color: #ffffff;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #6c63ff 0%, #8a84ff 100%);
    --gradient-secondary: linear-gradient(135deg, #63d1ff 0%, #4bb5e6 100%);
    --gradient-accent: linear-gradient(135deg, #ff6584 0%, #ff8a9b 100%);
    --gradient-light: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);

    /* Status Colors */
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --error-color: #f56565;
    --info-color: #4299e1;

    /* Spacing Scale */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;
    --space-4xl: 6rem;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-2xl: 24px;
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);
    --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);

    /* Transitions */
    --transition-fast: all 0.15s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    /* Layout */
    --container-width: 1200px;
    --container-padding: 1rem;

    /* Typography */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;

    /* Line Heights */
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--text-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--leading-relaxed);
    color: var(--text-color);
    background-color: var(--background-color);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Links */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-normal);
    cursor: pointer;
}

a:hover {
    color: var(--primary-dark);
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Lists */
ul, ol {
    list-style: none;
}

/* Images */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Form Elements */
input, button, textarea, select {
    font: inherit;
    color: inherit;
}

button {
    cursor: pointer;
    border: none;
    background: none;
}

/* Focus Management */
:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}

/* Selection */
::selection {
    background-color: var(--primary-color);
    color: var(--text-white);
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    font-weight: var(--font-weight-bold);
    line-height: var(--leading-tight);
    color: var(--text-color);
    margin-bottom: var(--space-lg);
    letter-spacing: -0.025em;
}

h1 {
    font-size: var(--text-5xl);
    font-weight: var(--font-weight-extrabold);
    line-height: var(--leading-tight);
    letter-spacing: -0.05em;
}

h2 {
    font-size: var(--text-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--leading-snug);
}

h3 {
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-semibold);
}

h4 {
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-semibold);
}

h5 {
    font-size: var(--text-xl);
    font-weight: var(--font-weight-medium);
}

h6 {
    font-size: var(--text-lg);
    font-weight: var(--font-weight-medium);
}

p {
    margin-bottom: var(--space-lg);
    line-height: var(--leading-relaxed);
    color: var(--text-color);
}

.text-large {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
}

.text-small {
    font-size: var(--text-sm);
    color: var(--text-light);
}

.text-xs {
    font-size: var(--text-xs);
    color: var(--text-lighter);
}

.text-muted {
    color: var(--text-light);
}

.text-light {
    color: var(--text-lighter);
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.font-medium {
    font-weight: var(--font-weight-medium);
}

.font-semibold {
    font-weight: var(--font-weight-semibold);
}

.font-bold {
    font-weight: var(--font-weight-bold);
}

.leading-tight {
    line-height: var(--leading-tight);
}

.leading-normal {
    line-height: var(--leading-normal);
}

.leading-relaxed {
    line-height: var(--leading-relaxed);
}

/* Modern Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-lg);
    font-family: var(--font-family-primary);
    font-size: var(--text-base);
    font-weight: var(--font-weight-semibold);
    line-height: 1;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    border: none;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
    -webkit-user-select: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.3);
}

/* Primary Button */
.btn-primary, .primary-btn {
    background: var(--gradient-primary);
    color: var(--text-white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover, .primary-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--text-white);
}

.btn-primary:active, .primary-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* Secondary Button */
.btn-secondary, .secondary-btn {
    background: var(--gradient-secondary);
    color: var(--text-white);
    box-shadow: var(--shadow-md);
}

.btn-secondary:hover, .secondary-btn:hover {
    background: #4bb5e6;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--text-white);
}

.btn-secondary:active, .secondary-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* Outline Button */
.btn-outline {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    box-shadow: none;
}

.btn-outline:hover {
    background: var(--gradient-primary);
    color: var(--text-white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* Ghost Button */
.btn-ghost {
    background-color: transparent;
    color: var(--primary-color);
    box-shadow: none;
    border: none;
}

.btn-ghost:hover {
    background-color: var(--background-light);
    color: var(--primary-dark);
}

/* Text Button */
.btn-text {
    background: none;
    border: none;
    color: var(--primary-color);
    padding: var(--space-sm) var(--space-md);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    box-shadow: none;
}

.btn-text:hover {
    color: var(--primary-dark);
    background-color: var(--background-light);
    border-radius: var(--radius-md);
}

/* Button Sizes */
.btn-small, .btn-sm {
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--text-sm);
    border-radius: var(--radius-md);
}

.btn-large, .btn-lg {
    padding: var(--space-lg) var(--space-2xl);
    font-size: var(--text-lg);
    border-radius: var(--radius-xl);
}

.btn-xl {
    padding: var(--space-xl) var(--space-3xl);
    font-size: var(--text-xl);
    border-radius: var(--radius-2xl);
}

/* Button with Icon */
.btn-icon {
    padding: var(--space-md);
    border-radius: var(--radius-full);
    width: 48px;
    height: 48px;
}

.btn-icon-sm {
    padding: var(--space-sm);
    border-radius: var(--radius-full);
    width: 36px;
    height: 36px;
}

/* Full Width Button */
.btn-full {
    width: 100%;
    justify-content: center;
}

/* Button Group */
.btn-group {
    display: inline-flex;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn-group .btn {
    border-radius: 0;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group .btn:first-child {
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.btn-group .btn:last-child {
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    border-right: none;
}

.btn-group .btn:only-child {
    border-radius: var(--radius-lg);
    border-right: none;
}

/* Enhanced Layout System */
.container {
    width: 100%;
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

.container-sm {
    max-width: 640px;
}

.container-md {
    max-width: 768px;
}

.container-lg {
    max-width: 1024px;
}

.container-xl {
    max-width: 1280px;
}

.container-2xl {
    max-width: 1536px;
}

/* Spacing Utilities */
.space-y-sm > * + * {
    margin-top: var(--space-sm);
}

.space-y-md > * + * {
    margin-top: var(--space-md);
}

.space-y-lg > * + * {
    margin-top: var(--space-lg);
}

.space-y-xl > * + * {
    margin-top: var(--space-xl);
}

.space-y-2xl > * + * {
    margin-top: var(--space-2xl);
}

/* Margin Utilities */
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }
.mr-0 { margin-right: 0; }

.m-sm { margin: var(--space-sm); }
.mt-sm { margin-top: var(--space-sm); }
.mb-sm { margin-bottom: var(--space-sm); }
.ml-sm { margin-left: var(--space-sm); }
.mr-sm { margin-right: var(--space-sm); }

.m-md { margin: var(--space-md); }
.mt-md { margin-top: var(--space-md); }
.mb-md { margin-bottom: var(--space-md); }
.ml-md { margin-left: var(--space-md); }
.mr-md { margin-right: var(--space-md); }

.m-lg { margin: var(--space-lg); }
.mt-lg { margin-top: var(--space-lg); }
.mb-lg { margin-bottom: var(--space-lg); }
.ml-lg { margin-left: var(--space-lg); }
.mr-lg { margin-right: var(--space-lg); }

.m-xl { margin: var(--space-xl); }
.mt-xl { margin-top: var(--space-xl); }
.mb-xl { margin-bottom: var(--space-xl); }
.ml-xl { margin-left: var(--space-xl); }
.mr-xl { margin-right: var(--space-xl); }

.m-2xl { margin: var(--space-2xl); }
.mt-2xl { margin-top: var(--space-2xl); }
.mb-2xl { margin-bottom: var(--space-2xl); }
.ml-2xl { margin-left: var(--space-2xl); }
.mr-2xl { margin-right: var(--space-2xl); }

/* Padding Utilities */
.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }
.pr-0 { padding-right: 0; }

.p-sm { padding: var(--space-sm); }
.pt-sm { padding-top: var(--space-sm); }
.pb-sm { padding-bottom: var(--space-sm); }
.pl-sm { padding-left: var(--space-sm); }
.pr-sm { padding-right: var(--space-sm); }

.p-md { padding: var(--space-md); }
.pt-md { padding-top: var(--space-md); }
.pb-md { padding-bottom: var(--space-md); }
.pl-md { padding-left: var(--space-md); }
.pr-md { padding-right: var(--space-md); }

.p-lg { padding: var(--space-lg); }
.pt-lg { padding-top: var(--space-lg); }
.pb-lg { padding-bottom: var(--space-lg); }
.pl-lg { padding-left: var(--space-lg); }
.pr-lg { padding-right: var(--space-lg); }

.p-xl { padding: var(--space-xl); }
.pt-xl { padding-top: var(--space-xl); }
.pb-xl { padding-bottom: var(--space-xl); }
.pl-xl { padding-left: var(--space-xl); }
.pr-xl { padding-right: var(--space-xl); }

.p-2xl { padding: var(--space-2xl); }
.pt-2xl { padding-top: var(--space-2xl); }
.pb-2xl { padding-bottom: var(--space-2xl); }
.pl-2xl { padding-left: var(--space-2xl); }
.pr-2xl { padding-right: var(--space-2xl); }

/* Flexbox Utilities */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.flex-row {
    flex-direction: row;
}

.flex-wrap {
    flex-wrap: wrap;
}

.items-center {
    align-items: center;
}

.items-start {
    align-items: flex-start;
}

.items-end {
    align-items: flex-end;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-around {
    justify-content: space-around;
}

.justify-start {
    justify-content: flex-start;
}

.justify-end {
    justify-content: flex-end;
}

.gap-sm {
    gap: var(--space-sm);
}

.gap-md {
    gap: var(--space-md);
}

.gap-lg {
    gap: var(--space-lg);
}

.gap-xl {
    gap: var(--space-xl);
}

/* Grid Utilities */
.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, 1fr);
}

.grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
}

.grid-gap-sm {
    gap: var(--space-sm);
}

.grid-gap-md {
    gap: var(--space-md);
}

.grid-gap-lg {
    gap: var(--space-lg);
}

.grid-gap-xl {
    gap: var(--space-xl);
}

/* Enhanced Header & Navigation */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--background-lighter);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition-normal);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-lg) var(--space-xl);
    max-width: var(--container-width);
    margin: 0 auto;
    min-height: 80px;
}

.logo {
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-extrabold);
    color: var(--primary-color);
    letter-spacing: -0.02em;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: var(--transition-normal);
}

.logo a {
    color: inherit;
    text-decoration: none;
    transition: var(--transition-normal);
}

.logo:hover {
    transform: scale(1.05);
}

.logo a:hover {
    text-shadow: 0 0 15px rgba(108, 99, 255, 0.5);
}

.nav-links {
    display: flex;
    gap: var(--space-2xl);
    align-items: center;
}

.nav-links a {
    color: var(--text-color);
    font-weight: var(--font-weight-medium);
    font-size: var(--text-base);
    position: relative;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-lg);
    transition: var(--transition-normal);
    text-decoration: none;
}

.nav-links a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-light);
    border-radius: var(--radius-lg);
    opacity: 0;
    transition: var(--transition-normal);
    z-index: -1;
}

.nav-links a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 4px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    border-radius: 1px;
    transition: var(--transition-normal);
}

.nav-links a:hover {
    color: var(--primary-color);
    transform: translateY(-1px);
}

.nav-links a:hover::before {
    opacity: 1;
}

.nav-links a:hover::after {
    width: 80%;
}

.nav-links a.active {
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
}

.nav-links a.active::after {
    width: 80%;
}

.profile-icon, .profile-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
}

.profile-icon a, .profile-btn a {
    font-size: 1.8rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.profile-icon a:hover, .profile-icon.active a,
.profile-btn a:hover, .profile-btn.active a {
    color: var(--primary-dark);
    transform: scale(1.1);
}

.user-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    object-fit: cover;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background-color: var(--text-color);
    margin: 2px 0;
    transition: var(--transition);
}

/* Enhanced Hero Section */
.hero {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-4xl) var(--container-padding);
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 50%, #e2e8f0 100%);
    min-height: 90vh;
    position: relative;
    overflow: hidden;
    text-align: center;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(108, 99, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(99, 209, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 101, 132, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    max-width: 800px;
    z-index: 2;
    animation: fadeInUp 1s ease-out;
    margin-bottom: var(--space-3xl);
}

.hero h1 {
    font-size: clamp(var(--text-4xl), 5vw, var(--text-6xl));
    font-weight: var(--font-weight-extrabold);
    line-height: var(--leading-tight);
    margin-bottom: var(--space-xl);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
}

.hero p {
    font-size: var(--text-xl);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-2xl);
    color: var(--text-light);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: var(--space-lg);
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: var(--space-2xl);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
    animation: floatUp 1.5s ease-out;
    filter: drop-shadow(0 10px 30px rgba(108, 99, 255, 0.2));
}

.meditation-svg {
    width: 100%;
    max-width: 500px;
    height: auto;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes floatUp {
    from { opacity: 0; transform: translateY(40px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Enhanced Feature Sections */
.feature {
    display: flex;
    align-items: center;
    padding: var(--space-4xl) var(--container-padding);
    max-width: var(--container-width);
    margin: 0 auto;
    gap: var(--space-4xl);
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.feature.visible {
    opacity: 1;
    transform: translateY(0);
}

.left-image {
    flex-direction: row;
}

.right-image {
    flex-direction: row-reverse;
}

.feature-content {
    flex: 1;
    max-width: 500px;
}

.feature-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.feature-image::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: var(--gradient-light);
    border-radius: var(--radius-2xl);
    opacity: 0.5;
    z-index: -1;
}

.feature h2 {
    font-size: var(--text-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    margin-bottom: var(--space-lg);
    line-height: var(--leading-tight);
}

.feature p {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--text-light);
    margin-bottom: var(--space-xl);
}

.learn-more {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
    font-size: var(--text-lg);
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-lg);
    background: rgba(108, 99, 255, 0.05);
    border: 2px solid transparent;
    transition: var(--transition-normal);
    text-decoration: none;
}

.learn-more:hover {
    color: var(--text-white);
    background: var(--gradient-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    gap: var(--space-md);
}

.learn-more i {
    transition: var(--transition-normal);
}

.learn-more:hover i {
    transform: translateX(4px);
}

.retreat-seeker-svg,
.retreat-guide-svg,
.retreat-host-svg {
    width: 100%;
    max-width: 400px;
    height: auto;
    filter: drop-shadow(0 10px 30px rgba(108, 99, 255, 0.1));
    transition: var(--transition-normal);
}

.feature-image:hover .retreat-seeker-svg,
.feature-image:hover .retreat-guide-svg,
.feature-image:hover .retreat-host-svg {
    transform: scale(1.05);
    filter: drop-shadow(0 15px 40px rgba(108, 99, 255, 0.2));
}

/* Enhanced CTA Section */
.cta-section {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 50%, #e2e8f0 100%);
    padding: var(--space-4xl) var(--container-padding);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(108, 99, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(99, 209, 255, 0.08) 0%, transparent 50%);
    pointer-events: none;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto var(--space-3xl);
    position: relative;
    z-index: 2;
    animation: fadeInUp 1s ease-out;
}

.cta-section h2 {
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    font-weight: var(--font-weight-extrabold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--space-lg);
    line-height: var(--leading-tight);
}

.cta-section p {
    font-size: var(--text-xl);
    line-height: var(--leading-relaxed);
    color: var(--text-light);
    margin-bottom: var(--space-2xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.trust-elements {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-2xl);
    margin-top: var(--space-4xl);
    position: relative;
    z-index: 2;
}

.trust-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.trust-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    background: rgba(255, 255, 255, 0.95);
}

.trust-item i {
    font-size: var(--text-3xl);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.trust-item span {
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    font-size: var(--text-lg);
}

.testimonial {
    grid-column: 1 / -1;
    max-width: 700px;
    margin: var(--space-2xl) auto 0;
    padding: var(--space-2xl);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
}

.testimonial::before {
    content: '\201C';
    position: absolute;
    top: -20px;
    left: var(--space-lg);
    font-size: 4rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    opacity: 0.7;
}

.testimonial p {
    font-style: italic;
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    color: var(--text-color);
    margin-bottom: var(--space-md);
}

.testimonial span {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-color);
    font-size: var(--text-base);
}

/* Notifications and Loading States */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
}

.notification {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification-success {
    border-left-color: var(--success-color);
}

.notification-error {
    border-left-color: var(--error-color);
}

.notification-warning {
    border-left-color: var(--warning-color);
}

.notification-info {
    border-left-color: var(--primary-color);
}

.notification-content {
    display: flex;
    align-items: center;
    padding: 1rem;
    gap: 0.75rem;
}

.notification-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.notification-success .notification-icon {
    color: var(--success-color);
}

.notification-error .notification-icon {
    color: var(--error-color);
}

.notification-warning .notification-icon {
    color: var(--warning-color);
}

.notification-info .notification-icon {
    color: var(--primary-color);
}

.notification-message {
    flex: 1;
    font-size: 0.9rem;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: var(--transition);
    flex-shrink: 0;
}

.notification-close:hover {
    background-color: var(--background-light);
    color: var(--text-color);
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: inherit;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--background-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-message {
    font-size: 0.9rem;
    color: var(--text-light);
    text-align: center;
}

/* Form Validation States */
.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--background-dark);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background-color: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(108, 99, 255, 0.1);
}

.form-group.error input,
.form-group.error textarea,
.form-group.error select {
    border-color: var(--error-color);
    box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.1);
}

.form-group.success input,
.form-group.success textarea,
.form-group.success select {
    border-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 0.5rem;
}

.strength-bar {
    height: 4px;
    border-radius: 2px;
    margin-bottom: 0.25rem;
    transition: var(--transition);
}

.strength-bar.strength-weak {
    background-color: var(--error-color);
    width: 33%;
}

.strength-bar.strength-medium {
    background-color: var(--warning-color);
    width: 66%;
}

.strength-bar.strength-strong {
    background-color: var(--success-color);
    width: 100%;
}

.strength-text {
    font-size: 0.8rem;
    color: var(--text-light);
}

.form-error {
    color: var(--error-color);
    font-size: 0.8rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.form-error i {
    font-size: 0.7rem;
}

/* Auth Error/Success Messages */
.auth-error {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    font-size: 0.9rem;
    border: 1px solid rgba(244, 67, 54, 0.2);
}

.auth-message.success {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    font-size: 0.9rem;
    border: 1px solid rgba(76, 175, 80, 0.2);
}

/* Improved Button States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    color: white;
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10001;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
}

/* Enhanced focus indicators for keyboard navigation */
.keyboard-navigation *:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.keyboard-navigation button:focus,
.keyboard-navigation a:focus,
.keyboard-navigation input:focus,
.keyboard-navigation textarea:focus,
.keyboard-navigation select:focus {
    box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.3);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --text-color: #000000;
        --background-color: #ffffff;
        --border-color: #000000;
    }

    .btn {
        border: 2px solid var(--text-color);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Improved color contrast for better readability */
.text-light {
    color: #555555; /* Improved contrast from #777 */
}

/* Better focus management for modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
}

.modal-content {
    position: relative;
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    width: 90%;
    max-width: 500px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Ensure interactive elements are large enough for touch */
.btn,
button,
a,
input,
textarea,
select {
    min-height: 44px;
    min-width: 44px;
}

/* Footer */
footer {
    background-color: #f8f9fa;
    padding: 4rem 2rem 1rem;
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: var(--container-width);
    margin: 0 auto;
    gap: 2rem;
}

.footer-logo {
    flex: 1;
    min-width: 200px;
}

.footer-logo p {
    margin-top: 0.5rem;
    color: var(--text-light);
    font-style: italic;
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 3rem;
}

.footer-column {
    min-width: 150px;
}

.footer-column h3 {
    font-size: 1.1rem;
    margin-bottom: 1.2rem;
    color: var(--primary-color);
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-column h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer-column a {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-light);
}

.footer-column a {
    display: block;
    margin-bottom: 0.8rem;
    color: var(--text-light);
    transition: var(--transition);
    position: relative;
    padding-left: 0;
}

.footer-column a:hover {
    color: var(--primary-color);
    padding-left: 5px;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: var(--container-width);
    margin: 3rem auto 0;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(0,0,0,0.05);
}

.social-icons {
    display: flex;
    gap: 1.2rem;
}

.social-icons a {
    font-size: 1.3rem;
    color: var(--text-light);
    transition: var(--transition);
}

.social-icons a:hover {
    color: var(--primary-color);
    transform: translateY(-3px);
}

/* Groups Page */
.groups-page main {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 2rem;
}

.groups-intro {
    text-align: center;
    margin-bottom: 3rem;
}

.groups-intro h1 {
    font-size: 2.5rem;
    color: var(--primary-dark);
}

.groups-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
}

.group-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    width: 100%;
    max-width: 350px;
    transition: var(--transition);
}

.group-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.group-image {
    height: 200px;
    background-color: #f9f7ff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.seeker-svg, .guide-svg, .host-svg {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.group-content {
    padding: 1.5rem;
    text-align: center;
}

.group-content h2 {
    color: var(--primary-dark);
    margin-bottom: 0.5rem;
}

.group-content p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

/* Browse Page */
.browse-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.browse-container {
    display: flex;
    flex: 1;
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 2rem;
    gap: 2rem;
}

.filters-sidebar {
    width: 300px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    height: fit-content;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.filter-group {
    margin-bottom: 1.5rem;
}

.filter-group h3 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
}

.date-inputs {
    display: flex;
    gap: 0.5rem;
}

.date-inputs input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
}

.price-slider {
    margin-top: 0.5rem;
}

.price-slider input {
    width: 100%;
}

.price-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-light);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.browse-content {
    flex: 1;
}

.browse-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.view-options {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
}

.view-btn.active {
    color: var(--primary-color);
    background-color: #f0f0f5;
}

.results-container {
    display: flex;
    gap: 1.5rem;
    height: 70vh;
}

.results-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 1rem;
}

.map-container {
    flex: 1;
    background-color: #f0f0f5;
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* Profile Page */
.profile-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.profile-container {
    display: flex;
    flex: 1;
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 2rem;
    gap: 2rem;
}

.profile-sidebar {
    width: 300px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    height: fit-content;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.profile-avatar {
    font-size: 3rem;
    color: var(--primary-color);
}

.profile-info h2 {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

.profile-info p {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 0;
}

.profile-nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.profile-nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    color: var(--text-color);
    transition: var(--transition);
}

.profile-nav-item:hover {
    background-color: #f9f7ff;
    color: var(--primary-color);
}

.profile-nav-item.active {
    background-color: #f0f0f5;
    color: var(--primary-color);
    font-weight: 600;
}

.profile-logout {
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.profile-content {
    flex: 1;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
}

.profile-tab {
    display: none;
}

.profile-tab.active {
    display: block;
}

.profile-tab h1 {
    margin-bottom: 2rem;
    color: var(--primary-dark);
}

.tab-description {
    margin-bottom: 2rem;
    color: var(--text-light);
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: #f9f7ff;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card i {
    font-size: 2rem;
    color: var(--primary-color);
}

.stat-info h3 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-dark);
    margin-bottom: 0;
}

.dashboard-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.dashboard-section h2 {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    color: var(--primary-dark);
}

.upcoming-retreat {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.retreat-image img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.retreat-info {
    flex: 1;
}

.retreat-info h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.retreat-info p {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 0.25rem;
}

.activity-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.activity-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.activity-info {
    flex: 1;
}

.activity-info h3 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.activity-info p {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-light);
}

.profile-form {
    max-width: 600px;
}

.form-group {
    margin-bottom: 1.8rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.9rem 1rem;
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
    background-color: #f9fafc;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1);
    background-color: white;
}

.form-group small {
    display: block;
    margin-top: 0.5rem;
    color: var(--text-light);
    font-size: 0.8rem;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-actions {
    margin-top: 2.5rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.group-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}

.group-membership-card {
    background-color: #f9f7ff;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.group-card-header {
    background-color: var(--primary-light);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.group-card-header h2 {
    margin-bottom: 0;
    font-size: 1.2rem;
}

.membership-status {
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.membership-status.active {
    color: var(--success-color);
}

.membership-status.inactive {
    color: var(--text-light);
}

.group-card-body {
    padding: 1.5rem;
}

.group-benefits {
    margin-top: 1rem;
}

.group-benefits li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.group-benefits li i {
    color: var(--success-color);
}

.group-card-footer {
    padding: 1.5rem;
    background-color: #f0f0f5;
    text-align: center;
}

/* Booking Confirmation Page */
.confirmation-page main {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 2rem;
}

.confirmation-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
}

.confirmation-header {
    text-align: center;
    margin-bottom: 2rem;
}

.confirmation-header i {
    font-size: 4rem;
    color: var(--success-color);
    margin-bottom: 1rem;
}

.confirmation-header h1 {
    color: var(--primary-dark);
    margin-bottom: 0.5rem;
}

.confirmation-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.confirmation-section {
    margin-bottom: 1.5rem;
}

.confirmation-section h2 {
    font-size: 1.2rem;
    color: var(--primary-dark);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.detail-label {
    font-weight: 500;
    color: var(--text-light);
}

.detail-value {
    font-weight: 600;
}

.status-confirmed {
    color: var(--success-color);
}

.total {
    font-size: 1.1rem;
    font-weight: 700;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #eee;
}

.retreat-info {
    display: flex;
    gap: 1rem;
}

.retreat-image img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.retreat-details h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.retreat-details p {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    color: var(--text-light);
}

.confirmation-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.confirmation-help {
    text-align: center;
    font-size: 0.9rem;
    color: var(--text-light);
}

.next-steps {
    text-align: center;
    margin-bottom: 3rem;
}

.next-steps h2 {
    color: var(--primary-dark);
    margin-bottom: 2rem;
}

.steps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.step-item {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    transition: var(--transition);
}

.step-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.step-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.step-content h3 {
    font-size: 1.1rem;
    color: var(--primary-dark);
    margin-bottom: 0.5rem;
}

.step-content p {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Auth Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow: auto;
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    max-width: 500px;
    width: 90%;
    position: relative;
}

.close-modal {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
}

.close-modal:hover {
    color: var(--text-color);
}

/* Login Page Styles */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 300px);
    padding: 2rem;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4eafc 100%);
}

.auth-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    max-width: 500px;
    width: 100%;
    padding: 2.5rem;
    animation: fadeIn 0.8s ease-out;
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.auth-tabs {
    display: flex;
    margin-bottom: 2.5rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.auth-tab {
    flex: 1;
    padding: 0.75rem 0;
    text-align: center;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 600;
    color: var(--text-light);
    transition: var(--transition);
    position: relative;
}

.auth-tab::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease, left 0.3s ease;
}

.auth-tab:hover {
    color: var(--primary-color);
}

.auth-tab.active {
    color: var(--primary-color);
}

.auth-tab.active::after {
    width: 100%;
    left: 0;
}

.auth-form {
    display: none;
    animation: fadeIn 0.5s ease-out;
}

.auth-form.active {
    display: block;
}

.auth-form h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--primary-color);
    font-size: 1.8rem;
}

.auth-options {
    text-align: center;
    margin: 1.5rem 0;
}

.auth-options a {
    color: var(--primary-color);
    transition: var(--transition);
}

.auth-options a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.social-auth {
    margin-top: 2.5rem;
    text-align: center;
}

.auth-error {
    background-color: #ffebee;
    color: #f44336;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.auth-message {
    padding: 0.75rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.auth-message.success {
    background-color: #e8f5e9;
    color: #4caf50;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.user-initial {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 600;
}

/* Payment Styles */
.payment-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
}

.payment-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
}

.payment-header {
    text-align: center;
    margin-bottom: 2rem;
}

.payment-summary {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.payment-summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.payment-total {
    font-size: 1.2rem;
    font-weight: 700;
    margin-top: 1rem;
}

.card-element-container {
    margin-bottom: 1.5rem;
}

#card-element {
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
}

#card-errors {
    color: #f44336;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.payment-message {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
    display: none;
}

.payment-message.error {
    background-color: #ffebee;
    color: #f44336;
}

.payment-message.success {
    background-color: #e8f5e9;
    color: #4caf50;
}

/* Payment form specific styles */
#payment-form .form-actions {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
}

#payment-form .btn {
    min-width: 120px;
}

.payment-security {
    margin-top: 2rem;
    text-align: center;
    color: var(--text-light);
    font-size: 0.9rem;
}

.payment-methods {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
    font-size: 1.5rem;
    color: var(--text-light);
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 0 var(--space-lg);
    }

    .hero {
        padding: var(--space-3xl) var(--space-lg);
    }

    .feature {
        padding: var(--space-3xl) var(--space-lg);
        gap: var(--space-3xl);
    }
}

@media (max-width: 1024px) {
    .hero, .feature {
        flex-direction: column;
        text-align: center;
    }

    .left-image, .right-image {
        flex-direction: column;
    }

    .feature-content {
        max-width: 100%;
    }

    .feature-image::before {
        display: none;
    }

    .trust-elements {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--space-lg);
    }

    .navbar {
        padding: var(--space-md) var(--space-lg);
    }

    .browse-container, .profile-container {
        flex-direction: column;
    }

    .filters-sidebar, .profile-sidebar {
        width: 100%;
    }

    .results-container {
        flex-direction: column;
        height: auto;
    }

    .map-container {
        height: 400px;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .hero {
        padding: var(--space-2xl) var(--space-md);
        min-height: 80vh;
    }

    .hero h1 {
        font-size: clamp(var(--text-3xl), 8vw, var(--text-5xl));
    }

    .hero p {
        font-size: var(--text-lg);
    }

    .feature {
        padding: var(--space-2xl) var(--space-md);
        gap: var(--space-2xl);
    }

    .feature h2 {
        font-size: var(--text-3xl);
    }

    .feature p {
        font-size: var(--text-base);
    }

    .cta-section {
        padding: var(--space-2xl) var(--space-md);
    }

    .cta-section h2 {
        font-size: clamp(var(--text-2xl), 6vw, var(--text-4xl));
    }

    .cta-section p {
        font-size: var(--text-lg);
    }

    .trust-elements {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    .trust-item {
        padding: var(--space-md);
    }

    .testimonial {
        padding: var(--space-lg);
        margin-top: var(--space-lg);
    }

    .footer-content {
        flex-direction: column;
        gap: var(--space-xl);
        text-align: center;
    }

    .dashboard-stats {
        grid-template-columns: 1fr 1fr;
    }

    .dashboard-sections {
        grid-template-columns: 1fr;
    }

    .group-cards {
        grid-template-columns: 1fr;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: var(--radius-lg);
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .btn-group .btn:last-child {
        border-bottom: none;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--space-md);
    }

    .hero {
        padding: var(--space-xl) var(--space-md);
    }

    .hero h1 {
        font-size: clamp(var(--text-2xl), 10vw, var(--text-4xl));
        margin-bottom: var(--space-md);
    }

    .hero p {
        font-size: var(--text-base);
        margin-bottom: var(--space-lg);
    }

    .cta-buttons {
        flex-direction: column;
        gap: var(--space-md);
        width: 100%;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .feature {
        padding: var(--space-xl) var(--space-md);
        gap: var(--space-xl);
    }

    .feature h2 {
        font-size: var(--text-2xl);
    }

    .learn-more {
        width: 100%;
        justify-content: center;
        padding: var(--space-lg);
    }

    .cta-section {
        padding: var(--space-xl) var(--space-md);
    }

    .trust-item {
        padding: var(--space-sm);
    }

    .trust-item i {
        font-size: var(--text-2xl);
    }

    .trust-item span {
        font-size: var(--text-base);
    }

    .testimonial {
        padding: var(--space-md);
    }

    .testimonial p {
        font-size: var(--text-base);
    }

    .navbar {
        padding: var(--space-md);
        min-height: 60px;
    }

    .logo {
        font-size: var(--text-xl);
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    .upcoming-retreat, .activity-item {
        flex-direction: column;
        text-align: center;
    }

    .notification-container {
        left: var(--space-sm);
        right: var(--space-sm);
        top: var(--space-sm);
        max-width: none;
    }
}

/* High DPI / Retina Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Enhanced Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        /* Dark Mode Colors */
        --text-color: #f7fafc;
        --text-light: #e2e8f0;
        --text-lighter: #cbd5e0;
        --text-white: #ffffff;

        /* Dark Backgrounds */
        --background-color: #1a202c;
        --background-light: #2d3748;
        --background-lighter: #4a5568;
        --background-dark: #718096;
        --surface-color: #2d3748;

        /* Dark Mode Gradients */
        --gradient-light: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);

        /* Ensure primary colors remain vibrant */
        --primary-color: #8a84ff;
        --primary-light: #a29dff;
        --primary-dark: #6c63ff;
        --secondary-color: #7dd3fc;
        --accent-color: #fbb6ce;
    }

    /* Body and base elements */
    body {
        background-color: var(--background-color);
        color: var(--text-color);
    }

    /* Header */
    header {
        background: rgba(26, 32, 44, 0.95);
        border-bottom-color: var(--background-lighter);
    }

    /* Navigation */
    .nav-links a {
        color: var(--text-color);
    }

    .nav-links a:hover {
        color: var(--primary-light);
    }

    .nav-links a.active {
        color: var(--primary-light);
    }

    /* Hero Section */
    .hero {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 50%, #4a5568 100%);
    }

    .hero h1 {
        background: linear-gradient(135deg, #8a84ff 0%, #a29dff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero p {
        color: var(--text-light);
    }

    /* Feature Sections */
    .feature h2 {
        color: var(--text-color);
    }

    .feature p {
        color: var(--text-light);
    }

    .feature-image::before {
        background: var(--gradient-light);
    }

    .learn-more {
        color: var(--primary-light);
        background: rgba(138, 132, 255, 0.1);
    }

    .learn-more:hover {
        color: var(--text-white);
        background: linear-gradient(135deg, #8a84ff 0%, #a29dff 100%);
    }

    /* CTA Section */
    .cta-section {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 50%, #4a5568 100%);
    }

    .cta-section h2 {
        background: linear-gradient(135deg, #8a84ff 0%, #a29dff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .cta-section p {
        color: var(--text-light);
    }

    /* Trust Elements */
    .trust-item {
        background: rgba(45, 55, 72, 0.8);
        border-color: rgba(113, 128, 150, 0.3);
        color: var(--text-color);
    }

    .trust-item:hover {
        background: rgba(45, 55, 72, 0.95);
    }

    .trust-item i {
        background: linear-gradient(135deg, #8a84ff 0%, #a29dff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .trust-item span {
        color: var(--text-color);
    }

    /* Testimonial */
    .testimonial {
        background: rgba(45, 55, 72, 0.9);
        border-color: rgba(113, 128, 150, 0.3);
    }

    .testimonial p {
        color: var(--text-color);
    }

    .testimonial span {
        color: var(--primary-light);
    }

    .testimonial::before {
        background: linear-gradient(135deg, #8a84ff 0%, #a29dff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Cards and surfaces - Force light backgrounds with dark text */
    .card,
    .surface,
    .group-card,
    .confirmation-card,
    .auth-card,
    .modal-content,
    .filters-sidebar,
    .browse-content,
    .group-membership-card .group-card-body,
    .group-card-footer {
        background-color: #ffffff !important;
        color: #2d3748 !important;
    }

    /* Forms - Force light backgrounds with dark text */
    .form-group input,
    .form-group textarea,
    .form-group select {
        background-color: #ffffff !important;
        color: #2d3748 !important;
        border-color: #e2e8f0;
    }

    .form-group input:focus,
    .form-group textarea:focus,
    .form-group select:focus {
        border-color: var(--primary-light);
        box-shadow: 0 0 0 2px rgba(138, 132, 255, 0.2);
    }

    .form-group label {
        color: #2d3748 !important;
    }

    /* Group cards content */
    .group-content h2,
    .group-content p {
        color: #2d3748 !important;
    }

    .group-content p {
        color: #718096 !important;
    }

    /* Buttons in dark mode */
    .btn-outline {
        border-color: var(--primary-light);
        color: var(--primary-light);
    }

    .btn-outline:hover {
        background: linear-gradient(135deg, #8a84ff 0%, #a29dff 100%);
        color: var(--text-white);
    }

    .btn-ghost:hover {
        background-color: var(--background-light);
        color: var(--primary-light);
    }

    .btn-text {
        color: var(--primary-light);
    }

    .btn-text:hover {
        color: var(--primary-light);
        background-color: var(--background-light);
    }

    /* Footer */
    footer {
        background-color: var(--background-color);
        color: var(--text-color);
    }

    footer::before {
        background: linear-gradient(90deg, #8a84ff, #7dd3fc, #fbb6ce);
    }

    .footer-logo p {
        color: var(--text-light);
    }

    .footer-links a {
        color: var(--text-light);
    }

    .footer-links a:hover {
        color: var(--primary-light);
    }

    .footer-links h3 {
        color: var(--text-color);
    }

    .footer-column h3::after {
        background-color: var(--primary-light);
    }

    .footer-bottom {
        border-top-color: rgba(255, 255, 255, 0.1);
        color: var(--text-light);
    }

    .social-icons a {
        color: var(--text-light);
    }

    .social-icons a:hover {
        color: var(--primary-light);
    }

    /* Dashboard and profile specific - Force dark text on light backgrounds */
    .dashboard-card,
    .profile-card,
    .stats-card,
    .stat-card,
    .profile-sidebar,
    .profile-content {
        background-color: #ffffff !important;
        color: #2d3748 !important;
    }

    .dashboard-stat-value,
    .profile-stat-value,
    .stat-value {
        color: #2d3748 !important;
    }

    .dashboard-stat-label,
    .profile-stat-label,
    .stat-info h3 {
        color: #718096 !important;
    }

    /* Profile sidebar text */
    .profile-info h2,
    .profile-nav-item {
        color: #2d3748 !important;
    }

    .profile-info p {
        color: #718096 !important;
    }

    /* Dashboard sections */
    .dashboard-section h2,
    .profile-tab h1 {
        color: #5046e5 !important;
    }

    .tab-description {
        color: #718096 !important;
    }

    /* Activity and retreat info */
    .activity-info h3,
    .retreat-info h3 {
        color: #2d3748 !important;
    }

    .activity-info p,
    .retreat-info p,
    .activity-time {
        color: #718096 !important;
    }

    /* Notifications in dark mode */
    .notification {
        background: var(--surface-color);
        color: var(--text-color);
    }

    .notification-message {
        color: var(--text-color);
    }

    /* Tables - Force light backgrounds with dark text */
    table {
        background-color: #ffffff !important;
        color: #2d3748 !important;
    }

    th {
        background-color: #f7fafc !important;
        color: #2d3748 !important;
    }

    td {
        border-color: #e2e8f0;
        color: #2d3748 !important;
    }

    /* Browse page specific */
    .browse-header h1,
    .filter-group h3 {
        color: #2d3748 !important;
    }

    .checkbox-group label {
        color: #2d3748 !important;
    }

    /* Confirmation page */
    .confirmation-header h1,
    .confirmation-section h2 {
        color: #5046e5 !important;
    }

    .detail-label {
        color: #718096 !important;
    }

    .detail-value {
        color: #2d3748 !important;
    }

    /* Profile page specific fixes */
    .profile-page .profile-sidebar,
    .profile-page .profile-content {
        background-color: #ffffff !important;
    }

    .profile-page .profile-header h2,
    .profile-page .profile-nav-item,
    .profile-page .dashboard-section h2,
    .profile-page .profile-tab h1 {
        color: #2d3748 !important;
    }

    .profile-page .profile-info p,
    .profile-page .tab-description {
        color: #718096 !important;
    }

    .profile-page .stat-card {
        background-color: #f9f7ff !important;
    }

    .profile-page .stat-info h3 {
        color: #2d3748 !important;
    }

    .profile-page .stat-value {
        color: #5046e5 !important;
    }

    .profile-page .activity-info h3,
    .profile-page .retreat-info h3 {
        color: #2d3748 !important;
    }

    .profile-page .activity-info p,
    .profile-page .retreat-info p,
    .profile-page .activity-time {
        color: #718096 !important;
    }

    /* Group membership cards */
    .profile-page .group-card-body {
        background-color: #ffffff !important;
        color: #2d3748 !important;
    }

    .profile-page .group-card-body p {
        color: #718096 !important;
    }

    .profile-page .group-card-footer {
        background-color: #f0f0f5 !important;
    }

    /* Links */
    a {
        color: var(--primary-light);
    }

    a:hover {
        color: var(--primary-color);
    }

    /* Headings */
    h1, h2, h3, h4, h5, h6 {
        color: var(--text-color);
    }

    /* Specific page elements */
    .about-hero {
        background: linear-gradient(135deg, #8a84ff 0%, #6c63ff 100%);
    }

    .about-hero h1,
    .about-hero p {
        color: var(--text-white);
    }

    /* Ensure all text is readable */
    p, span, div, li {
        color: inherit;
    }

    /* Override any remaining light text on light backgrounds */
    .text-muted {
        color: var(--text-light) !important;
    }

    .text-light {
        color: var(--text-lighter) !important;
    }
}
