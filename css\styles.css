/* Base Styles */
:root {
    --primary-color: #6c63ff;
    --primary-light: #8a84ff;
    --primary-dark: #5046e5;
    --secondary-color: #63d1ff;
    --accent-color: #ff6584;
    --text-color: #333;
    --text-light: #777;
    --background-color: #fff;
    --background-light: #f9f9f9;
    --background-dark: #f0f0f0;
    --secondary-bg: #f8f9fa;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --border-radius: 8px;
    --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --container-width: 1200px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--background-color);
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    font-size: 1rem;
    box-shadow: 0 4px 6px rgba(108, 99, 255, 0.1);
}

.btn-primary, .primary-btn {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover, .primary-btn:hover {
    background-color: var(--primary-dark);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(108, 99, 255, 0.2);
}

.btn-secondary, .secondary-btn {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover, .secondary-btn:hover {
    background-color: #4bb5e6;
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(99, 209, 255, 0.2);
}

.btn-outline {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-text {
    background: none;
    border: none;
    color: var(--primary-color);
    padding: 0;
    font-weight: 600;
    cursor: pointer;
}

.btn-text:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Layout */
.container {
    width: 100%;
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header & Navigation */
header {
    background-color: white;
    box-shadow: var(--box-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: var(--container-width);
    margin: 0 auto;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    letter-spacing: -0.5px;
}

.logo a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.logo a:hover {
    text-shadow: 0 0 15px rgba(108, 99, 255, 0.5);
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-links a {
    color: var(--text-color);
    font-weight: 500;
    position: relative;
    padding: 0.5rem 0;
}

.nav-links a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.nav-links a:hover::after {
    width: 100%;
}

.profile-icon, .profile-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
}

.profile-icon a, .profile-btn a {
    font-size: 1.8rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.profile-icon a:hover, .profile-icon.active a,
.profile-btn a:hover, .profile-btn.active a {
    color: var(--primary-dark);
    transform: scale(1.1);
}

.user-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    object-fit: cover;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background-color: var(--text-color);
    margin: 2px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    display: flex;
    flex-direction: column;
    padding: 2rem 1rem;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4eafc 100%);
    min-height: 85vh;
    justify-content: center;
    position: relative;
    overflow: hidden;
    max-width: 100%;
    margin: 0;
}

.hero-content {
    flex: 1;
    max-width: 600px;
    z-index: 1;
    animation: fadeIn 1s ease-out;
    margin: 0 auto;
}

.hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.hero p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: var(--text-light);
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    flex-direction: column;
    max-width: 300px;
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
    animation: floatUp 1.5s ease-out;
}

.meditation-svg {
    width: 100%;
    max-width: 500px;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes floatUp {
    from { opacity: 0; transform: translateY(40px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Feature Sections */
.feature {
    display: flex;
    align-items: center;
    padding: 5rem 2rem;
    max-width: var(--container-width);
    margin: 0 auto;
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.feature.visible {
    opacity: 1;
    transform: translateY(0);
}

.left-image {
    flex-direction: row;
}

.right-image {
    flex-direction: row-reverse;
}

.feature-content {
    flex: 1;
    padding: 0 2rem;
}

.feature-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.feature h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 2rem;
}

.feature p {
    margin-bottom: 1.5rem;
    color: var(--text-light);
    font-size: 1.05rem;
    line-height: 1.6;
}

.learn-more {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    font-weight: 600;
    gap: 0.5rem;
    transition: var(--transition);
}

.learn-more:hover {
    color: var(--primary-dark);
    gap: 0.8rem;
}

.retreat-seeker-svg, .retreat-guide-svg, .retreat-host-svg {
    width: 100%;
    max-width: 400px;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #e4eafc 100%);
    padding: 5rem 2rem;
    text-align: center;
    max-width: 100%;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto 3rem;
    animation: fadeIn 1s ease-out;
}

.cta-section h2 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    color: var(--text-light);
}

.trust-elements {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2.5rem;
    margin-top: 4rem;
    animation: fadeIn 1.5s ease-out;
}

.trust-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
    transition: var(--transition);
}

.trust-item:hover {
    transform: translateY(-5px);
}

.trust-item i {
    font-size: 2.2rem;
    color: var(--primary-color);
}

.testimonial {
    width: 100%;
    max-width: 600px;
    margin-top: 3rem;
    padding: 2rem;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    position: relative;
}

.testimonial::before {
    content: '\201C';
    position: absolute;
    top: -30px;
    left: 20px;
    font-size: 5rem;
    color: var(--primary-light);
    opacity: 0.3;
}

.testimonial p {
    font-style: italic;
    margin-bottom: 0.5rem;
}

.testimonial span {
    font-weight: 600;
    color: var(--primary-dark);
}

/* Footer */
footer {
    background-color: #f8f9fa;
    padding: 4rem 2rem 1rem;
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: var(--container-width);
    margin: 0 auto;
    gap: 2rem;
}

.footer-logo {
    flex: 1;
    min-width: 200px;
}

.footer-logo p {
    margin-top: 0.5rem;
    color: var(--text-light);
    font-style: italic;
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 3rem;
}

.footer-column {
    min-width: 150px;
}

.footer-column h3 {
    font-size: 1.1rem;
    margin-bottom: 1.2rem;
    color: var(--primary-color);
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-column h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer-column a {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-light);
}

.footer-column a {
    display: block;
    margin-bottom: 0.8rem;
    color: var(--text-light);
    transition: var(--transition);
    position: relative;
    padding-left: 0;
}

.footer-column a:hover {
    color: var(--primary-color);
    padding-left: 5px;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: var(--container-width);
    margin: 3rem auto 0;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(0,0,0,0.05);
}

.social-icons {
    display: flex;
    gap: 1.2rem;
}

.social-icons a {
    font-size: 1.3rem;
    color: var(--text-light);
    transition: var(--transition);
}

.social-icons a:hover {
    color: var(--primary-color);
    transform: translateY(-3px);
}

/* Groups Page */
.groups-page main {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 2rem;
}

.groups-intro {
    text-align: center;
    margin-bottom: 3rem;
}

.groups-intro h1 {
    font-size: 2.5rem;
    color: var(--primary-dark);
}

.groups-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
}

.group-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    width: 100%;
    max-width: 350px;
    transition: var(--transition);
}

.group-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.group-image {
    height: 200px;
    background-color: #f9f7ff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.seeker-svg, .guide-svg, .host-svg {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.group-content {
    padding: 1.5rem;
    text-align: center;
}

.group-content h2 {
    color: var(--primary-dark);
    margin-bottom: 0.5rem;
}

.group-content p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

/* Browse Page */
.browse-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.browse-container {
    display: flex;
    flex: 1;
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 2rem;
    gap: 2rem;
}

.filters-sidebar {
    width: 300px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    height: fit-content;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.filter-group {
    margin-bottom: 1.5rem;
}

.filter-group h3 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
}

.date-inputs {
    display: flex;
    gap: 0.5rem;
}

.date-inputs input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
}

.price-slider {
    margin-top: 0.5rem;
}

.price-slider input {
    width: 100%;
}

.price-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-light);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.browse-content {
    flex: 1;
}

.browse-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.view-options {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
}

.view-btn.active {
    color: var(--primary-color);
    background-color: #f0f0f5;
}

.results-container {
    display: flex;
    gap: 1.5rem;
    height: 70vh;
}

.results-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 1rem;
}

.map-container {
    flex: 1;
    background-color: #f0f0f5;
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* Profile Page */
.profile-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.profile-container {
    display: flex;
    flex: 1;
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 2rem;
    gap: 2rem;
}

.profile-sidebar {
    width: 300px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    height: fit-content;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.profile-avatar {
    font-size: 3rem;
    color: var(--primary-color);
}

.profile-info h2 {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

.profile-info p {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 0;
}

.profile-nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.profile-nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    color: var(--text-color);
    transition: var(--transition);
}

.profile-nav-item:hover {
    background-color: #f9f7ff;
    color: var(--primary-color);
}

.profile-nav-item.active {
    background-color: #f0f0f5;
    color: var(--primary-color);
    font-weight: 600;
}

.profile-logout {
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.profile-content {
    flex: 1;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
}

.profile-tab {
    display: none;
}

.profile-tab.active {
    display: block;
}

.profile-tab h1 {
    margin-bottom: 2rem;
    color: var(--primary-dark);
}

.tab-description {
    margin-bottom: 2rem;
    color: var(--text-light);
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: #f9f7ff;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card i {
    font-size: 2rem;
    color: var(--primary-color);
}

.stat-info h3 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-dark);
    margin-bottom: 0;
}

.dashboard-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.dashboard-section h2 {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    color: var(--primary-dark);
}

.upcoming-retreat {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.retreat-image img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.retreat-info {
    flex: 1;
}

.retreat-info h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.retreat-info p {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 0.25rem;
}

.activity-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.activity-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.activity-info {
    flex: 1;
}

.activity-info h3 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.activity-info p {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-light);
}

.profile-form {
    max-width: 600px;
}

.form-group {
    margin-bottom: 1.8rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.9rem 1rem;
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
    background-color: #f9fafc;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1);
    background-color: white;
}

.form-group small {
    display: block;
    margin-top: 0.5rem;
    color: var(--text-light);
    font-size: 0.8rem;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-actions {
    margin-top: 2.5rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.group-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}

.group-membership-card {
    background-color: #f9f7ff;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.group-card-header {
    background-color: var(--primary-light);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.group-card-header h2 {
    margin-bottom: 0;
    font-size: 1.2rem;
}

.membership-status {
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.membership-status.active {
    color: var(--success-color);
}

.membership-status.inactive {
    color: var(--text-light);
}

.group-card-body {
    padding: 1.5rem;
}

.group-benefits {
    margin-top: 1rem;
}

.group-benefits li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.group-benefits li i {
    color: var(--success-color);
}

.group-card-footer {
    padding: 1.5rem;
    background-color: #f0f0f5;
    text-align: center;
}

/* Booking Confirmation Page */
.confirmation-page main {
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 2rem;
}

.confirmation-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
}

.confirmation-header {
    text-align: center;
    margin-bottom: 2rem;
}

.confirmation-header i {
    font-size: 4rem;
    color: var(--success-color);
    margin-bottom: 1rem;
}

.confirmation-header h1 {
    color: var(--primary-dark);
    margin-bottom: 0.5rem;
}

.confirmation-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.confirmation-section {
    margin-bottom: 1.5rem;
}

.confirmation-section h2 {
    font-size: 1.2rem;
    color: var(--primary-dark);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.detail-label {
    font-weight: 500;
    color: var(--text-light);
}

.detail-value {
    font-weight: 600;
}

.status-confirmed {
    color: var(--success-color);
}

.total {
    font-size: 1.1rem;
    font-weight: 700;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #eee;
}

.retreat-info {
    display: flex;
    gap: 1rem;
}

.retreat-image img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.retreat-details h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.retreat-details p {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    color: var(--text-light);
}

.confirmation-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.confirmation-help {
    text-align: center;
    font-size: 0.9rem;
    color: var(--text-light);
}

.next-steps {
    text-align: center;
    margin-bottom: 3rem;
}

.next-steps h2 {
    color: var(--primary-dark);
    margin-bottom: 2rem;
}

.steps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.step-item {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    transition: var(--transition);
}

.step-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.step-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.step-content h3 {
    font-size: 1.1rem;
    color: var(--primary-dark);
    margin-bottom: 0.5rem;
}

.step-content p {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Auth Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow: auto;
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    max-width: 500px;
    width: 90%;
    position: relative;
}

.close-modal {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
}

.close-modal:hover {
    color: var(--text-color);
}

/* Login Page Styles */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 300px);
    padding: 2rem;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4eafc 100%);
}

.auth-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    max-width: 500px;
    width: 100%;
    padding: 2.5rem;
    animation: fadeIn 0.8s ease-out;
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.auth-tabs {
    display: flex;
    margin-bottom: 2.5rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.auth-tab {
    flex: 1;
    padding: 0.75rem 0;
    text-align: center;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 600;
    color: var(--text-light);
    transition: var(--transition);
    position: relative;
}

.auth-tab::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease, left 0.3s ease;
}

.auth-tab:hover {
    color: var(--primary-color);
}

.auth-tab.active {
    color: var(--primary-color);
}

.auth-tab.active::after {
    width: 100%;
    left: 0;
}

.auth-form {
    display: none;
    animation: fadeIn 0.5s ease-out;
}

.auth-form.active {
    display: block;
}

.auth-form h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--primary-color);
    font-size: 1.8rem;
}

.auth-options {
    text-align: center;
    margin: 1.5rem 0;
}

.auth-options a {
    color: var(--primary-color);
    transition: var(--transition);
}

.auth-options a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.social-auth {
    margin-top: 2.5rem;
    text-align: center;
}

.auth-error {
    background-color: #ffebee;
    color: #f44336;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.auth-message {
    padding: 0.75rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.auth-message.success {
    background-color: #e8f5e9;
    color: #4caf50;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.user-initial {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 600;
}

/* Payment Styles */
.payment-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
}

.payment-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
}

.payment-header {
    text-align: center;
    margin-bottom: 2rem;
}

.payment-summary {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.payment-summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.payment-total {
    font-size: 1.2rem;
    font-weight: 700;
    margin-top: 1rem;
}

.card-element-container {
    margin-bottom: 1.5rem;
}

#card-element {
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
}

#card-errors {
    color: #f44336;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.payment-message {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
    display: none;
}

.payment-message.error {
    background-color: #ffebee;
    color: #f44336;
}

.payment-message.success {
    background-color: #e8f5e9;
    color: #4caf50;
}

/* Payment form specific styles */
#payment-form .form-actions {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
}

#payment-form .btn {
    min-width: 120px;
}

.payment-security {
    margin-top: 2rem;
    text-align: center;
    color: var(--text-light);
    font-size: 0.9rem;
}

.payment-methods {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
    font-size: 1.5rem;
    color: var(--text-light);
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .hero, .feature {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .feature-right {
        flex-direction: column;
    }

    .feature-content {
        padding: 0;
    }

    .cta-buttons {
        justify-content: center;
    }

    .browse-container, .profile-container {
        flex-direction: column;
    }

    .filters-sidebar, .profile-sidebar {
        width: 100%;
    }

    .results-container {
        flex-direction: column;
        height: auto;
    }

    .map-container {
        height: 400px;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .footer-content {
        flex-direction: column;
    }

    .dashboard-stats {
        grid-template-columns: 1fr 1fr;
    }

    .dashboard-sections {
        grid-template-columns: 1fr;
    }

    .group-cards {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .upcoming-retreat, .activity-item {
        flex-direction: column;
    }
}
