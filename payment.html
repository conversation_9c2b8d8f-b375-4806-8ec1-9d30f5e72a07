<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindField - Payment</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="payment-page">
    <header>
        <nav class="navbar">
            <div class="logo"><a href="index.html">MindField</a></div>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="about.html">About</a>
                <a href="groups.html">Groups</a>
                <a href="browse.html">Browse</a>
                <a href="index.html#contact">Contact</a>
            </div>
            <div class="profile-icon">
                <a href="login.html"><i class="fas fa-user-circle"></i></a>
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <main class="payment-container">
        <div class="payment-card animate-in">
            <div class="payment-header">
                <h1>Complete Your Booking</h1>
                <p>You're just one step away from your transformative retreat experience.</p>
            </div>

            <div class="payment-summary">
                <h2>Booking Summary</h2>
                <div class="retreat-info">
                    <div class="retreat-image">
                        <img src="https://via.placeholder.com/100x100" alt="Retreat" id="retreat-image">
                    </div>
                    <div class="retreat-details">
                        <h3 id="retreat-title">Mindfulness in the Mountains</h3>
                        <p><i class="fas fa-map-marker-alt"></i> <span id="retreat-location">Bali, Indonesia</span></p>
                        <p><i class="fas fa-calendar"></i> <span id="retreat-dates">June 15-22, 2023</span></p>
                        <p><i class="fas fa-user"></i> <span id="retreat-guide">Led by Sarah Johnson</span></p>
                    </div>
                </div>

                <div class="payment-summary-items">
                    <div class="payment-summary-item">
                        <span>Retreat Cost (7 nights)</span>
                        <span id="retreat-cost">$1,200.00</span>
                    </div>
                    <div class="payment-summary-item">
                        <span>Taxes & Fees</span>
                        <span id="retreat-fees">$120.00</span>
                    </div>
                    <div class="payment-summary-item payment-total">
                        <span>Total</span>
                        <span id="retreat-total">$1,320.00</span>
                    </div>
                </div>
            </div>

            <div class="payment-form-container">
                <h2>Payment Information</h2>
                <form id="payment-form" data-amount="1320" data-currency="usd" data-description="Mindfulness in the Mountains Retreat" data-retreat-id="retreat123" data-start-date="2023-06-15" data-end-date="2023-06-22">
                    <div class="form-group">
                        <label for="cardholder-name">Cardholder Name</label>
                        <input type="text" id="cardholder-name" required>
                    </div>

                    <div class="form-group">
                        <label for="card-element">Credit or Debit Card</label>
                        <div id="card-element" class="card-element-container">
                            <!-- Stripe Card Element will be inserted here -->
                        </div>
                        <div id="card-errors" role="alert"></div>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="save-card" checked>
                            Save this card for future bookings
                        </label>
                    </div>

                    <div id="payment-message" class="payment-message"></div>

                    <div class="form-actions">
                        <a href="browse.html" class="btn btn-outline">Cancel</a>
                        <button type="submit" class="btn btn-primary">Pay Now</button>
                    </div>
                </form>
            </div>

            <div class="payment-security">
                <p><i class="fas fa-lock"></i> Your payment is secure. We use industry-standard encryption to protect your information.</p>
                <div class="payment-methods">
                    <i class="fab fa-cc-visa"></i>
                    <i class="fab fa-cc-mastercard"></i>
                    <i class="fab fa-cc-amex"></i>
                    <i class="fab fa-cc-discover"></i>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <div class="logo"><a href="index.html">MindField</a></div>
                <p>Transform Your Life</p>
            </div>
            <div class="footer-links">
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="#">About Us</a>
                    <a href="#">Careers</a>
                    <a href="#">Press</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Blog</a>
                    <a href="#">Community</a>
                    <a href="#">Guides</a>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <a href="#">Help Center</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 MindField. All rights reserved.</p>
            <div class="social-icons">
                <a href="#"><i class="fab fa-facebook"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-storage.js"></script>

    <!-- Stripe SDK -->
    <script src="https://js.stripe.com/v3/"></script>

    <!-- App Scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/security.js"></script>
    <script src="js/performance.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/database.js"></script>
    <script src="js/payment.js"></script>
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/simple-test-user.js"></script>

    <!-- Payment Initialization Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize payment form
            initPaymentForm();

            // Get retreat ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const retreatId = urlParams.get('id');

            if (retreatId) {
                // In a real app, this would fetch the retreat details from the database
                // For demo purposes, we'll use mock data
                loadRetreatDetails(retreatId);
            }
        });

        function loadRetreatDetails(retreatId) {
            // In a real app, this would fetch the retreat from Firestore
            // For demo purposes, we'll use mock data

            // Update form data attributes
            const form = document.getElementById('payment-form');
            form.setAttribute('data-retreat-id', retreatId);
        }
    </script>
</body>
</html>
