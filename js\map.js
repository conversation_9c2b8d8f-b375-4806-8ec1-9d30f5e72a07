// Map functionality for MindField

document.addEventListener('DOMContentLoaded', function() {
    // Initialize map if we're on the browse page
    if (document.querySelector('#map')) {
        initMap();
    }
});

// Initialize the map
function initMap() {
    // Create map centered on a default location (world center)
    const map = L.map('map').setView([20, 0], 2);
    
    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);
    
    // Get the type of browse from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const browseType = urlParams.get('type') || 'retreats';
    
    // Load markers based on the browse type
    loadMapMarkers(map, browseType);
    
    // Add map interaction with the results list
    addMapInteraction(map);
}

// Load markers on the map based on the browse type
function loadMapMarkers(map, type) {
    // Get data based on type
    let data = [];
    
    switch(type) {
        case 'retreats':
            data = generateMockRetreats();
            break;
        case 'guides':
            data = generateMockGuides();
            break;
        case 'spaces':
            data = generateMockSpaces();
            break;
        default:
            data = generateMockRetreats();
    }
    
    // Create a marker group to manage all markers
    const markers = L.featureGroup();
    
    // Add markers for each item
    data.forEach(item => {
        if (item.coordinates) {
            // Create custom marker icon
            const customIcon = L.divIcon({
                className: 'custom-marker',
                html: `<div class="marker-content marker-${type}" data-id="${item.id}">
                        <i class="fas ${getMarkerIcon(type)}"></i>
                        <span class="marker-price">${item.price.replace('$', '')}</span>
                      </div>`,
                iconSize: [40, 40],
                iconAnchor: [20, 40]
            });
            
            // Create marker and add to group
            const marker = L.marker([item.coordinates.lat, item.coordinates.lng], {
                icon: customIcon,
                title: item.title
            }).addTo(map);
            
            // Add popup with information
            marker.bindPopup(`
                <div class="map-popup">
                    <h3>${item.title}</h3>
                    <p><i class="fas fa-map-marker-alt"></i> ${item.location}</p>
                    <p>${item.price} ${item.pricePeriod}</p>
                    <a href="#" class="btn-text view-details" data-id="${item.id}">View Details</a>
                </div>
            `);
            
            // Add marker to group
            markers.addLayer(marker);
            
            // Add marker click event
            marker.on('click', function() {
                highlightResult(item.id);
            });
        }
    });
    
    // Add marker group to map
    markers.addTo(map);
    
    // Fit map to show all markers
    if (markers.getLayers().length > 0) {
        map.fitBounds(markers.getBounds(), {
            padding: [50, 50]
        });
    }
}

// Get appropriate icon for marker based on type
function getMarkerIcon(type) {
    switch(type) {
        case 'retreats':
            return 'fa-om';
        case 'guides':
            return 'fa-user';
        case 'spaces':
            return 'fa-home';
        default:
            return 'fa-map-marker-alt';
    }
}

// Add interaction between map and results list
function addMapInteraction(map) {
    // Listen for clicks on result items to highlight corresponding marker
    document.addEventListener('click', function(e) {
        // Check if clicked element is a result item or within one
        const resultItem = e.target.closest('.result-item');
        
        if (resultItem) {
            const itemId = resultItem.dataset.id;
            
            if (itemId) {
                // Find and click the corresponding marker
                const marker = document.querySelector(`.custom-marker[data-id="${itemId}"]`);
                
                if (marker) {
                    // Trigger marker click
                    marker.click();
                    
                    // Get coordinates from data
                    const data = getCurrentBrowseData();
                    const item = data.find(i => i.id.toString() === itemId);
                    
                    if (item && item.coordinates) {
                        // Center map on marker
                        map.setView([item.coordinates.lat, item.coordinates.lng], 12, {
                            animate: true,
                            duration: 1
                        });
                    }
                }
            }
        }
    });
    
    // Listen for "View Details" clicks in popups
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('view-details')) {
            e.preventDefault();
            
            const itemId = e.target.dataset.id;
            
            if (itemId) {
                highlightResult(itemId);
            }
        }
    });
    
    // Add map move/zoom events to update visible results
    map.on('moveend', function() {
        updateVisibleResults(map);
    });
}

// Highlight a result in the list
function highlightResult(id) {
    // Remove highlight from all results
    const allResults = document.querySelectorAll('.result-item');
    allResults.forEach(item => item.classList.remove('highlighted'));
    
    // Add highlight to the selected result
    const selectedResult = document.querySelector(`.result-item[data-id="${id}"]`);
    
    if (selectedResult) {
        selectedResult.classList.add('highlighted');
        selectedResult.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
    }
}

// Update visible results based on map bounds
function updateVisibleResults(map) {
    const bounds = map.getBounds();
    const data = getCurrentBrowseData();
    const resultItems = document.querySelectorAll('.result-item');
    
    // Match result items with data
    resultItems.forEach(item => {
        const itemId = item.dataset.id;
        const dataItem = data.find(d => d.id.toString() === itemId);
        
        if (dataItem && dataItem.coordinates) {
            // Check if marker is within current map bounds
            const isVisible = bounds.contains([dataItem.coordinates.lat, dataItem.coordinates.lng]);
            
            // Update visibility class
            if (isVisible) {
                item.classList.remove('out-of-bounds');
            } else {
                item.classList.add('out-of-bounds');
            }
        }
    });
    
    // Update results count
    updateResultsCount();
}

// Update the count of visible results
function updateResultsCount() {
    const visibleResults = document.querySelectorAll('.result-item:not(.out-of-bounds)').length;
    const totalResults = document.querySelectorAll('.result-item').length;
    
    const browseTitle = document.getElementById('browse-title');
    
    if (browseTitle) {
        const titleText = browseTitle.textContent.split('(')[0].trim();
        browseTitle.textContent = `${titleText} (${visibleResults} of ${totalResults})`;
    }
}

// Get current browse data based on URL parameter
function getCurrentBrowseData() {
    const urlParams = new URLSearchParams(window.location.search);
    const browseType = urlParams.get('type') || 'retreats';
    
    switch(browseType) {
        case 'retreats':
            return generateMockRetreats();
        case 'guides':
            return generateMockGuides();
        case 'spaces':
            return generateMockSpaces();
        default:
            return generateMockRetreats();
    }
}
