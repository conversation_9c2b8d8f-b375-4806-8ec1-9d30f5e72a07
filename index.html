<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindField - Transform Your Life</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- EMERGENCY BUTTON FIX - COMPLETE OVERRIDE -->
    <style>
        /* RADICAL APPROACH - Override hero layout completely */
        .hero {
            display: block !important;
            text-align: center !important;
        }

        .hero-content {
            display: block !important;
            text-align: center !important;
            max-width: 800px !important;
            margin: 0 auto !important;
        }

        .cta-buttons {
            display: block !important;
            text-align: center !important;
            width: 100% !important;
            margin: 0 auto !important;
        }

        .hero .cta-buttons .btn,
        .hero .cta-buttons .primary-btn,
        .hero .cta-buttons .secondary-btn,
        .cta-section .cta-content .btn,
        .cta-section .cta-content .primary-btn,
        .cta-section .cta-content .secondary-btn {
            display: inline-block !important;
            width: auto !important;
            min-width: auto !important;
            max-width: none !important;
            flex: none !important;
            flex-grow: 0 !important;
            flex-shrink: 0 !important;
            flex-basis: auto !important;
            margin: 0 8px 8px 0 !important;
            vertical-align: top !important;
            box-sizing: border-box !important;
            padding: 12px 24px !important;
            border-radius: 8px !important;
            text-decoration: none !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
            cursor: pointer !important;
            border: none !important;
            font-size: 1rem !important;
            line-height: 1.5 !important;
        }

        /* Remove any flex properties completely */
        .hero .cta-buttons .btn,
        .hero .cta-buttons .primary-btn,
        .hero .cta-buttons .secondary-btn,
        .cta-section .cta-content .btn,
        .cta-section .cta-content .primary-btn,
        .cta-section .cta-content .secondary-btn {
            flex: none !important;
            align-self: auto !important;
        }

        /* Prevent expansion in all states */
        .hero .cta-buttons .btn:hover,
        .hero .cta-buttons .btn:focus,
        .hero .cta-buttons .btn:active,
        .hero .cta-buttons .primary-btn:hover,
        .hero .cta-buttons .primary-btn:focus,
        .hero .cta-buttons .primary-btn:active,
        .hero .cta-buttons .secondary-btn:hover,
        .hero .cta-buttons .secondary-btn:focus,
        .hero .cta-buttons .secondary-btn:active,
        .cta-section .cta-content .btn:hover,
        .cta-section .cta-content .btn:focus,
        .cta-section .cta-content .btn:active,
        .cta-section .cta-content .primary-btn:hover,
        .cta-section .cta-content .primary-btn:focus,
        .cta-section .cta-content .primary-btn:active,
        .cta-section .cta-content .secondary-btn:hover,
        .cta-section .cta-content .secondary-btn:focus,
        .cta-section .cta-content .secondary-btn:active {
            display: inline-block !important;
            width: auto !important;
            min-width: auto !important;
            max-width: none !important;
            flex: none !important;
            transform: translateY(-2px) !important;
        }

        /* Force override any computed styles */
        .hero .cta-buttons .btn[style],
        .hero .cta-buttons .primary-btn[style],
        .hero .cta-buttons .secondary-btn[style],
        .cta-section .cta-content .btn[style],
        .cta-section .cta-content .primary-btn[style],
        .cta-section .cta-content .secondary-btn[style] {
            width: auto !important;
            display: inline-block !important;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo"><a href="index.html">MindField</a></div>
            <div class="nav-links">
                <a href="about.html">About</a>
                <a href="curated-memories.html">Curated</a>
                <a href="#features">Features</a>
                <a href="#testimonials">Testimonials</a>
                <a href="#contact">Contact</a>
            </div>
            <div class="profile-icon">
                <a href="login.html"><i class="fas fa-user-circle"></i></a>
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <main>
        <!-- Section 1: Hero -->
        <section class="hero">
            <div class="hero-content">
                <h1>Transform Your Life, Now It's The Time</h1>
                <p>Discover life-changing yoga and meditation retreats that connect your mind, body, and soul. Join a community of seekers, guides, and hosts on the journey to inner peace.</p>
                <div class="cta-buttons">
                    <button class="btn primary-btn">Download Now</button>
                    <button class="btn secondary-btn" id="enter-btn">Enter MindField</button>
                </div>
            </div>
            <div class="hero-image">
                <svg class="meditation-svg" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
                    <!-- Simple meditation figure SVG -->
                    <ellipse cx="300" cy="320" rx="120" ry="30" fill="#e0e0e0" />
                    <circle cx="300" cy="180" r="60" fill="#f5f5f5" stroke="#6c63ff" stroke-width="2" />
                    <path d="M300 240 L300 300 Q240 330 300 350 Q360 330 300 300 Z" fill="#6c63ff" stroke="#6c63ff" stroke-width="2" />
                    <path d="M240 200 L200 240" stroke="#6c63ff" stroke-width="2" />
                    <path d="M360 200 L400 240" stroke="#6c63ff" stroke-width="2" />
                </svg>
            </div>
        </section>

        <!-- Section 2: Feature 1 - Group A -->
        <section class="feature left-image">
            <div class="feature-image">
                <svg class="retreat-seeker-svg" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
                    <!-- Retreat seeker SVG -->
                    <rect x="150" y="100" width="300" height="200" rx="10" fill="#f0f8ff" stroke="#6c63ff" stroke-width="2" />
                    <circle cx="250" cy="150" r="30" fill="#63d1ff" />
                    <path d="M230 200 L270 200 L250 230 Z" fill="#63d1ff" />
                    <line x1="200" y1="250" x2="400" y2="250" stroke="#6c63ff" stroke-width="2" stroke-dasharray="5,5" />
                    <circle cx="350" cy="200" r="20" fill="#ff6584" />
                </svg>
            </div>
            <div class="feature-content">
                <h2>Find Your Perfect Retreat</h2>
                <p>Looking for a transformative experience? Browse hundreds of curated retreats worldwide. Filter by location, duration, style, and price to find the perfect match for your spiritual journey.</p>
                <a href="groups.html?group=a" class="learn-more">Explore Retreats <i class="fas fa-arrow-right"></i></a>
            </div>
        </section>

        <!-- Section 3: Feature 2 - Group B -->
        <section class="feature right-image">
            <div class="feature-content">
                <h2>Share Your Wisdom</h2>
                <p>Are you a yoga instructor, meditation guide, or spiritual teacher? Connect with seekers from around the world and lead transformative retreats in beautiful locations.</p>
                <a href="groups.html?group=b" class="learn-more">Become a Guide <i class="fas fa-arrow-right"></i></a>
            </div>
            <div class="feature-image">
                <svg class="retreat-guide-svg" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
                    <!-- Retreat guide SVG -->
                    <circle cx="300" cy="150" r="50" fill="#f5f5f5" stroke="#6c63ff" stroke-width="2" />
                    <path d="M250 200 L350 200 L300 300 Z" fill="#6c63ff" stroke="#6c63ff" stroke-width="2" />
                    <circle cx="250" cy="220" r="15" fill="#f5f5f5" stroke="#6c63ff" stroke-width="2" />
                    <circle cx="350" cy="220" r="15" fill="#f5f5f5" stroke="#6c63ff" stroke-width="2" />
                    <path d="M270 150 L330 150" stroke="#6c63ff" stroke-width="2" />
                </svg>
            </div>
        </section>

        <!-- Section 4: Feature 3 - Group C -->
        <section class="feature left-image">
            <div class="feature-image">
                <svg class="retreat-host-svg" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
                    <!-- Retreat host SVG -->
                    <path d="M200 150 L400 150 L350 250 L250 250 Z" fill="#e6e6fa" stroke="#6c63ff" stroke-width="2" />
                    <path d="M250 250 L350 250 L320 300 L280 300 Z" fill="#d8bfd8" stroke="#6c63ff" stroke-width="2" />
                    <path d="M250 150 L350 150 L320 100 L280 100 Z" fill="#d8bfd8" stroke="#6c63ff" stroke-width="2" />
                    <circle cx="300" cy="200" r="30" fill="#fff" stroke="#6c63ff" stroke-width="2" />
                </svg>
            </div>
            <div class="feature-content">
                <h2>Host Transformative Experiences</h2>
                <p>Have a serene space perfect for retreats? Share your sanctuary with the world. From mountain cabins to beachfront villas, turn your property into a haven for mindfulness and growth.</p>
                <a href="groups.html?group=c" class="learn-more">List Your Space <i class="fas fa-arrow-right"></i></a>
            </div>
        </section>

        <!-- Section 5: Final CTA -->
        <section class="cta-section">
            <div class="cta-content">
                <h2>Ready to Change Your Life?</h2>
                <p>Join thousands of seekers, guides, and hosts who have already discovered the transformative power of MindField retreats.</p>
                <button class="btn primary-btn">Download Now</button>
            </div>
            <div class="trust-elements">
                <div class="trust-item">
                    <i class="fas fa-star"></i>
                    <span>4.9/5 Average Rating</span>
                </div>
                <div class="trust-item">
                    <i class="fas fa-users"></i>
                    <span>50,000+ Active Users</span>
                </div>
                <div class="trust-item">
                    <i class="fas fa-globe"></i>
                    <span>1,000+ Retreats Worldwide</span>
                </div>
                <div class="testimonial">
                    <p>"MindField changed my life. I found my purpose during a retreat in Bali and now I'm a certified yoga instructor!"</p>
                    <span>— Sarah J., New York</span>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <div class="logo"><a href="index.html">MindField</a></div>
                <p>Transform Your Life</p>
            </div>
            <div class="footer-links">
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="about.html">About Us</a>
                    <a href="#">Careers</a>
                    <a href="#">Press</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Blog</a>
                    <a href="#">Community</a>
                    <a href="curated-memories.html">Curated</a>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <a href="#">Help Center</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 MindField. All rights reserved.</p>
            <div class="social-icons">
                <a href="#"><i class="fab fa-facebook"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-storage.js"></script>

    <!-- Stripe SDK -->
    <script src="https://js.stripe.com/v3/"></script>

    <!-- App Scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/error-handler.js"></script>
    <script src="js/form-validator.js"></script>
    <script src="js/accessibility.js"></script>
    <script src="js/performance-monitor.js"></script>
    <script src="js/security.js"></script>
    <script src="js/performance.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/database.js"></script>
    <script src="js/payment.js"></script>
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/simple-test-user.js"></script>

    <!-- EMERGENCY BUTTON FIX - JAVASCRIPT BACKUP -->
    <script>
        // Force button sizes after page load using inline-block approach
        document.addEventListener('DOMContentLoaded', function() {
            function fixButtonSizes() {
                // Target all buttons in hero and CTA sections
                const selectors = [
                    '.hero .cta-buttons .btn',
                    '.hero .cta-buttons .primary-btn',
                    '.hero .cta-buttons .secondary-btn',
                    '.cta-section .cta-content .btn',
                    '.cta-section .cta-content .primary-btn',
                    '.cta-section .cta-content .secondary-btn'
                ];

                // Fix container first
                const containers = document.querySelectorAll('.cta-buttons');
                containers.forEach(container => {
                    container.style.setProperty('display', 'block', 'important');
                    container.style.setProperty('text-align', 'center', 'important');
                    container.style.setProperty('width', '100%', 'important');
                });

                selectors.forEach(selector => {
                    const buttons = document.querySelectorAll(selector);
                    buttons.forEach(button => {
                        // Force inline-block display and remove all flex properties
                        button.style.setProperty('display', 'inline-block', 'important');
                        button.style.setProperty('width', 'auto', 'important');
                        button.style.setProperty('min-width', 'auto', 'important');
                        button.style.setProperty('max-width', 'none', 'important');
                        button.style.setProperty('flex', 'none', 'important');
                        button.style.setProperty('flex-grow', '0', 'important');
                        button.style.setProperty('flex-shrink', '0', 'important');
                        button.style.setProperty('flex-basis', 'auto', 'important');
                        button.style.setProperty('align-self', 'auto', 'important');
                        button.style.setProperty('margin', '0 8px 8px 0', 'important');
                        button.style.setProperty('vertical-align', 'top', 'important');
                        button.style.setProperty('box-sizing', 'border-box', 'important');

                        // Override any existing event listeners by adding our own that runs after
                        const originalAddEventListener = button.addEventListener;

                        // Add event listeners to prevent expansion on interaction
                        ['mouseenter', 'mouseleave', 'focus', 'blur', 'click', 'mousedown', 'mouseup'].forEach(event => {
                            button.addEventListener(event, function() {
                                // Force our styles after any other event handlers
                                setTimeout(() => {
                                    this.style.setProperty('display', 'inline-block', 'important');
                                    this.style.setProperty('width', 'auto', 'important');
                                    this.style.setProperty('min-width', 'auto', 'important');
                                    this.style.setProperty('max-width', 'none', 'important');
                                    this.style.setProperty('flex', 'none', 'important');
                                }, 0);
                            }, true); // Use capture phase to run first
                        });
                    });
                });
            }

            // Fix immediately
            fixButtonSizes();

            // Fix again after a short delay to catch any dynamic changes
            setTimeout(fixButtonSizes, 100);
            setTimeout(fixButtonSizes, 500);
            setTimeout(fixButtonSizes, 1000);

            // Monitor for changes and fix continuously
            const observer = new MutationObserver(fixButtonSizes);
            observer.observe(document.body, {
                attributes: true,
                attributeFilter: ['style', 'class'],
                subtree: true
            });

            // Debug: Log button styles
            setTimeout(() => {
                const buttons = document.querySelectorAll('.cta-buttons button');
                buttons.forEach((button, index) => {
                    const computedStyle = window.getComputedStyle(button);
                    console.log(`Button ${index + 1}:`, {
                        display: computedStyle.display,
                        width: computedStyle.width,
                        minWidth: computedStyle.minWidth,
                        maxWidth: computedStyle.maxWidth,
                        flex: computedStyle.flex,
                        flexGrow: computedStyle.flexGrow,
                        flexShrink: computedStyle.flexShrink,
                        flexBasis: computedStyle.flexBasis
                    });
                });
            }, 2000);
        });
    </script>
</body>
</html>
