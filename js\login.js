// Login page specific functionality

document.addEventListener('DOMContentLoaded', function() {
    // Initialize auth tabs
    initAuthTabs();
    
    // Setup form submissions
    setupFormSubmissions();
    
    // Check if user is already logged in
    checkLoggedInStatus();
});

// Initialize auth tabs
function initAuthTabs() {
    const authTabs = document.querySelectorAll('.auth-tab');
    const authForms = document.querySelectorAll('.auth-form');
    
    authTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            authTabs.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Hide all forms
            authForms.forEach(form => form.classList.remove('active'));
            
            // Show selected form
            const formId = this.getAttribute('data-tab') + '-form';
            document.getElementById(formId).classList.add('active');
        });
    });
    
    // Forgot password link
    const forgotPassword = document.getElementById('forgot-password');
    if (forgotPassword) {
        forgotPassword.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Hide all forms
            authForms.forEach(form => form.classList.remove('active'));
            
            // Show reset form
            document.getElementById('reset-form').classList.add('active');
        });
    }
    
    // Back to login button
    const backToLogin = document.getElementById('back-to-login');
    if (backToLogin) {
        backToLogin.addEventListener('click', function() {
            // Hide all forms
            authForms.forEach(form => form.classList.remove('active'));
            
            // Show login form
            document.getElementById('login-form').classList.add('active');
            
            // Update tabs
            authTabs.forEach(t => t.classList.remove('active'));
            document.querySelector('[data-tab="login"]').classList.add('active');
        });
    }
}

// Setup form submissions
function setupFormSubmissions() {
    // Login form
    const loginButton = document.getElementById('login-button');
    if (loginButton) {
        loginButton.addEventListener('click', function() {
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            
            if (email && password) {
                loginWithEmail(email, password);
            } else {
                showAuthError('login-error', 'Please enter both email and password');
            }
        });
    }
    
    // Signup form
    const signupButton = document.getElementById('signup-button');
    if (signupButton) {
        signupButton.addEventListener('click', function() {
            const name = document.getElementById('signup-name').value;
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;
            const confirm = document.getElementById('signup-confirm').value;
            
            if (name && email && password && confirm) {
                if (password === confirm) {
                    if (isPasswordStrong(password)) {
                        signupWithEmail(name, email, password);
                    } else {
                        showAuthError('signup-error', 'Password must be at least 8 characters with a number and special character');
                    }
                } else {
                    showAuthError('signup-error', 'Passwords do not match');
                }
            } else {
                showAuthError('signup-error', 'Please fill in all fields');
            }
        });
    }
    
    // Reset password form
    const resetButton = document.getElementById('reset-button');
    if (resetButton) {
        resetButton.addEventListener('click', function() {
            const email = document.getElementById('reset-email').value;
            
            if (email) {
                resetPassword(email);
            } else {
                showAuthError('reset-message', 'Please enter your email address');
                document.getElementById('reset-message').style.display = 'block';
            }
        });
    }
    
    // Google login
    const googleLogin = document.getElementById('google-login');
    if (googleLogin) {
        googleLogin.addEventListener('click', function() {
            loginWithGoogle();
        });
    }
    
    // Google signup
    const googleSignup = document.getElementById('google-signup');
    if (googleSignup) {
        googleSignup.addEventListener('click', function() {
            loginWithGoogle();
        });
    }
}

// Check if user is already logged in
function checkLoggedInStatus() {
    if (typeof auth !== 'undefined' && auth) {
        auth.onAuthStateChanged(user => {
            if (user) {
                // User is already logged in, redirect to profile
                window.location.href = 'profile.html';
            }
        });
    }
}

// Login with email
function loginWithEmail(email, password) {
    if (typeof auth !== 'undefined' && auth) {
        auth.signInWithEmailAndPassword(email, password)
            .then((userCredential) => {
                // Redirect to profile page
                window.location.href = 'profile.html';
            })
            .catch((error) => {
                console.error('Login error:', error);
                showAuthError('login-error', getAuthErrorMessage(error.code));
            });
    } else {
        console.log('Firebase Auth not available');
        // For demo purposes, simulate login
        window.location.href = 'profile.html';
    }
}

// Signup with email
function signupWithEmail(name, email, password) {
    if (typeof auth !== 'undefined' && auth) {
        auth.createUserWithEmailAndPassword(email, password)
            .then((userCredential) => {
                // Update profile
                return userCredential.user.updateProfile({
                    displayName: name
                }).then(() => {
                    // Create user document in Firestore
                    return createUserDocument(userCredential.user.uid, {
                        name: name,
                        email: email,
                        createdAt: new Date()
                    });
                }).then(() => {
                    // Redirect to profile page
                    window.location.href = 'profile.html';
                });
            })
            .catch((error) => {
                console.error('Signup error:', error);
                showAuthError('signup-error', getAuthErrorMessage(error.code));
            });
    } else {
        console.log('Firebase Auth not available');
        // For demo purposes, simulate signup
        window.location.href = 'profile.html';
    }
}

// Login with Google
function loginWithGoogle() {
    if (typeof auth !== 'undefined' && auth && typeof firebase !== 'undefined') {
        const provider = new firebase.auth.GoogleAuthProvider();
        
        auth.signInWithPopup(provider)
            .then((result) => {
                // Check if user document exists
                return checkUserDocument(result.user.uid, {
                    name: result.user.displayName,
                    email: result.user.email,
                    photoURL: result.user.photoURL,
                    createdAt: new Date()
                });
            })
            .then(() => {
                // Redirect to profile page
                window.location.href = 'profile.html';
            })
            .catch((error) => {
                console.error('Google login error:', error);
                showAuthError('login-error', getAuthErrorMessage(error.code));
            });
    } else {
        console.log('Firebase Auth not available');
        // For demo purposes, simulate login
        window.location.href = 'profile.html';
    }
}

// Reset password
function resetPassword(email) {
    if (typeof auth !== 'undefined' && auth) {
        auth.sendPasswordResetEmail(email)
            .then(() => {
                // Show success message
                const resetMessage = document.getElementById('reset-message');
                resetMessage.textContent = 'Password reset email sent. Check your inbox.';
                resetMessage.className = 'auth-message success';
                resetMessage.style.display = 'block';
                
                // Clear email field
                document.getElementById('reset-email').value = '';
                
                // Return to login after 3 seconds
                setTimeout(() => {
                    // Hide all forms
                    document.querySelectorAll('.auth-form').forEach(form => form.classList.remove('active'));
                    
                    // Show login form
                    document.getElementById('login-form').classList.add('active');
                    
                    // Update tabs
                    document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
                    document.querySelector('[data-tab="login"]').classList.add('active');
                }, 3000);
            })
            .catch((error) => {
                console.error('Password reset error:', error);
                showAuthError('reset-message', getAuthErrorMessage(error.code));
                document.getElementById('reset-message').style.display = 'block';
            });
    } else {
        console.log('Firebase Auth not available');
        // For demo purposes, simulate reset
        const resetMessage = document.getElementById('reset-message');
        resetMessage.textContent = 'Password reset email sent. Check your inbox.';
        resetMessage.className = 'auth-message success';
        resetMessage.style.display = 'block';
        
        // Clear email field
        document.getElementById('reset-email').value = '';
        
        // Return to login after 3 seconds
        setTimeout(() => {
            // Hide all forms
            document.querySelectorAll('.auth-form').forEach(form => form.classList.remove('active'));
            
            // Show login form
            document.getElementById('login-form').classList.add('active');
            
            // Update tabs
            document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
            document.querySelector('[data-tab="login"]').classList.add('active');
        }, 3000);
    }
}

// Create user document in Firestore
function createUserDocument(userId, userData) {
    if (typeof db !== 'undefined' && db) {
        return db.collection('users').doc(userId).set(userData);
    } else {
        console.log('Firestore not available');
        return Promise.resolve();
    }
}

// Check if user document exists, create if not
function checkUserDocument(userId, userData) {
    if (typeof db !== 'undefined' && db) {
        return db.collection('users').doc(userId).get()
            .then((doc) => {
                if (!doc.exists) {
                    // Create user document
                    return db.collection('users').doc(userId).set(userData);
                }
                return Promise.resolve();
            });
    } else {
        console.log('Firestore not available');
        return Promise.resolve();
    }
}

// Show auth error
function showAuthError(elementId, message) {
    const errorElement = document.getElementById(elementId);
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    }
}

// Get auth error message
function getAuthErrorMessage(errorCode) {
    switch (errorCode) {
        case 'auth/user-not-found':
            return 'No account found with this email address';
        case 'auth/wrong-password':
            return 'Incorrect password';
        case 'auth/email-already-in-use':
            return 'This email is already registered';
        case 'auth/weak-password':
            return 'Password is too weak';
        case 'auth/invalid-email':
            return 'Invalid email address';
        case 'auth/operation-not-allowed':
            return 'Operation not allowed';
        case 'auth/account-exists-with-different-credential':
            return 'An account already exists with the same email address but different sign-in credentials';
        case 'auth/popup-closed-by-user':
            return 'Sign-in popup was closed before completing the sign-in';
        default:
            return 'An error occurred. Please try again.';
    }
}

// Check if password is strong
function isPasswordStrong(password) {
    // At least 8 characters, 1 number, 1 special character
    const regex = /^(?=.*[0-9])(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]{8,}$/;
    return regex.test(password);
}
