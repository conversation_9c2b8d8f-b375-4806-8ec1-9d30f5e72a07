// Form validation tests for MindField

// Test form validation functionality
testFramework.test('Form Validator - Email Validation', async () => {
  // Create test form
  const form = testFramework.createElement('form', {}, `
    <div class="form-group">
      <label for="test-email">Email</label>
      <input type="email" id="test-email" required>
    </div>
  `);
  document.body.appendChild(form);

  const emailInput = form.querySelector('#test-email');
  
  // Test invalid email
  testFramework.simulateInput(emailInput, 'invalid-email');
  emailInput.dispatchEvent(new Event('blur'));
  
  await testFramework.wait(100);
  
  const formGroup = emailInput.closest('.form-group');
  testFramework.assertTrue(formGroup.classList.contains('error'), 'Form group should have error class for invalid email');
  
  // Test valid email
  testFramework.simulateInput(emailInput, '<EMAIL>');
  emailInput.dispatchEvent(new Event('blur'));
  
  await testFramework.wait(100);
  
  testFramework.assertTrue(formGroup.classList.contains('success'), 'Form group should have success class for valid email');
  
  // Cleanup
  document.body.removeChild(form);
});

testFramework.test('Form Validator - Password Strength', async () => {
  // Create test form
  const form = testFramework.createElement('form', {}, `
    <div class="form-group">
      <label for="test-password">Password</label>
      <input type="password" id="test-password" required>
    </div>
  `);
  document.body.appendChild(form);

  const passwordInput = form.querySelector('#test-password');
  
  // Test weak password
  testFramework.simulateInput(passwordInput, 'weak');
  passwordInput.dispatchEvent(new Event('input'));
  
  await testFramework.wait(200);
  
  const strengthIndicator = form.querySelector('.password-strength');
  testFramework.assertNotNull(strengthIndicator, 'Password strength indicator should appear');
  
  const weakBar = form.querySelector('.strength-bar.strength-weak');
  testFramework.assertNotNull(weakBar, 'Weak password should show weak strength bar');
  
  // Test strong password
  testFramework.simulateInput(passwordInput, 'StrongPass123!');
  passwordInput.dispatchEvent(new Event('input'));
  
  await testFramework.wait(200);
  
  const strongBar = form.querySelector('.strength-bar.strength-strong');
  testFramework.assertNotNull(strongBar, 'Strong password should show strong strength bar');
  
  // Cleanup
  document.body.removeChild(form);
});

testFramework.test('Form Validator - Required Field Validation', async () => {
  // Create test form
  const form = testFramework.createElement('form', {}, `
    <div class="form-group">
      <label for="test-required">Required Field</label>
      <input type="text" id="test-required" required>
    </div>
  `);
  document.body.appendChild(form);

  const requiredInput = form.querySelector('#test-required');
  
  // Test empty required field
  testFramework.simulateInput(requiredInput, '');
  requiredInput.dispatchEvent(new Event('blur'));
  
  await testFramework.wait(100);
  
  const formGroup = requiredInput.closest('.form-group');
  testFramework.assertTrue(formGroup.classList.contains('error'), 'Empty required field should show error');
  
  const errorMessage = form.querySelector('.form-error');
  testFramework.assertNotNull(errorMessage, 'Error message should appear for empty required field');
  
  // Test filled required field
  testFramework.simulateInput(requiredInput, 'Valid input');
  requiredInput.dispatchEvent(new Event('blur'));
  
  await testFramework.wait(100);
  
  testFramework.assertTrue(formGroup.classList.contains('success'), 'Filled required field should show success');
  
  // Cleanup
  document.body.removeChild(form);
});

testFramework.test('Form Validator - Password Confirmation', async () => {
  // Create test form
  const form = testFramework.createElement('form', {}, `
    <div class="form-group">
      <label for="signup-password">Password</label>
      <input type="password" id="signup-password" required>
    </div>
    <div class="form-group">
      <label for="signup-confirm">Confirm Password</label>
      <input type="password" id="signup-confirm" required>
    </div>
  `);
  document.body.appendChild(form);

  const passwordInput = form.querySelector('#signup-password');
  const confirmInput = form.querySelector('#signup-confirm');
  
  // Set password
  testFramework.simulateInput(passwordInput, 'TestPassword123!');
  
  // Test non-matching confirmation
  testFramework.simulateInput(confirmInput, 'DifferentPassword');
  confirmInput.dispatchEvent(new Event('input'));
  
  await testFramework.wait(100);
  
  const confirmGroup = confirmInput.closest('.form-group');
  testFramework.assertTrue(confirmGroup.classList.contains('error'), 'Non-matching password confirmation should show error');
  
  // Test matching confirmation
  testFramework.simulateInput(confirmInput, 'TestPassword123!');
  confirmInput.dispatchEvent(new Event('input'));
  
  await testFramework.wait(100);
  
  testFramework.assertTrue(confirmGroup.classList.contains('success'), 'Matching password confirmation should show success');
  
  // Cleanup
  document.body.removeChild(form);
});

testFramework.test('Form Validator - Real-time Validation', async () => {
  // Create test form
  const form = testFramework.createElement('form', {}, `
    <div class="form-group">
      <label for="test-realtime">Email</label>
      <input type="email" id="test-realtime" required>
    </div>
  `);
  document.body.appendChild(form);

  const emailInput = form.querySelector('#test-realtime');
  const formGroup = emailInput.closest('.form-group');
  
  // Test focus clears validation
  emailInput.focus();
  testFramework.assertFalse(formGroup.classList.contains('error'), 'Focus should clear validation state');
  testFramework.assertFalse(formGroup.classList.contains('success'), 'Focus should clear validation state');
  
  // Test blur triggers validation
  testFramework.simulateInput(emailInput, 'invalid');
  emailInput.dispatchEvent(new Event('blur'));
  
  await testFramework.wait(100);
  
  testFramework.assertTrue(formGroup.classList.contains('error'), 'Blur should trigger validation');
  
  // Cleanup
  document.body.removeChild(form);
});
