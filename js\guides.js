/**
 * MindField Guides Page JavaScript
 * Handles guide showcase functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize animations
    const animateElements = document.querySelectorAll('.animate-in');
    animateElements.forEach(element => {
        element.classList.add('visible');
    });

    // Filter functionality
    const filterButtons = document.querySelectorAll('.filter-btn');
    const guideCards = document.querySelectorAll('.guide-card');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Get filter value
            const filterValue = this.getAttribute('data-filter');
            
            // Filter cards
            guideCards.forEach(card => {
                if (filterValue === 'all' || card.getAttribute('data-category').includes(filterValue)) {
                    card.style.display = 'block';
                    // Add a small animation when cards appear
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 50);
                } else {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    setTimeout(() => {
                        card.style.display = 'none';
                    }, 300);
                }
            });
        });
    });

    // Load more functionality (simulated)
    const loadMoreButton = document.querySelector('.load-more');
    if (loadMoreButton) {
        loadMoreButton.addEventListener('click', function() {
            // In a real app, this would load more guides from the database
            // For this demo, we'll just show a message
            this.textContent = 'Loading...';
            
            setTimeout(() => {
                this.textContent = 'No More Guides to Load';
                this.disabled = true;
                this.style.backgroundColor = '#ccc';
                this.style.cursor = 'not-allowed';
            }, 1500);
        });
    }

    // Fade in sections on scroll
    const fadeInSections = document.querySelectorAll('.fade-in-section');
    
    function checkFade() {
        fadeInSections.forEach(section => {
            const sectionTop = section.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            if (sectionTop < windowHeight * 0.85) {
                section.classList.add('is-visible');
            }
        });
    }
    
    // Initial check
    checkFade();
    
    // Check on scroll
    window.addEventListener('scroll', checkFade);
});
