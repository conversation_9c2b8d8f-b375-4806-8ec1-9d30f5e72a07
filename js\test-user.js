// Test User Functionality for MindField
// This file provides a way to create and manage a test user with admin privileges

// Immediately invoke the setup function to ensure it runs
(function() {
  console.log('Test user script loaded');

  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM loaded, setting up test user controls');
      setupTestUserControls();
    });
  } else {
    // DOM is already ready
    console.log('DOM already loaded, setting up test user controls');
    setupTestUserControls();
  }
})();

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Test@123',
  name: 'Test Admin',
  isAdmin: true
};

// Setup test user controls
function setupTestUserControls() {
  console.log('Setting up test user controls');
  // Create test user panel
  const testPanel = document.createElement('div');
  testPanel.className = 'test-user-panel';
  testPanel.innerHTML = `
    <div class="test-user-toggle">
      <span style="font-weight: bold;">TU</span>
    </div>
    <div class="test-user-content">
      <h3>Test User Controls</h3>
      <p>Email: ${TEST_USER.email}</p>
      <p>Password: ${TEST_USER.password}</p>
      <div class="test-user-actions">
        <button id="create-test-user" class="btn primary-btn">Create Test User</button>
        <button id="login-test-user" class="btn secondary-btn">Login as Test User</button>
        <button id="delete-test-user" class="btn btn-danger">Delete Test User</button>
      </div>
      <div id="test-user-status" class="test-user-status"></div>
    </div>
  `;

  document.body.appendChild(testPanel);
  console.log('Test user panel appended to body');

  // Add styles for test user panel
  const style = document.createElement('style');
  style.textContent = `
    .test-user-panel {
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 9999;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .test-user-toggle {
      width: 50px;
      height: 50px;
      background-color: #ff6584; /* Using a bright color to make it more visible */
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      cursor: pointer;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
      transition: var(--transition);
      font-size: 16px;
      border: 3px solid white;
    }

    .test-user-toggle:hover {
      transform: scale(1.1);
    }

    .test-user-content {
      display: none;
      background-color: white;
      border-radius: var(--border-radius);
      padding: 1.5rem;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      margin-bottom: 10px;
      width: 300px;
      position: absolute;
      bottom: 60px;
      right: 0;
      border: 2px solid #ff6584;
    }

    .test-user-content h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .test-user-content p {
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
    }

    .test-user-actions {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      margin-top: 1rem;
    }

    .btn-danger {
      background-color: #f44336;
      color: white;
    }

    .btn-danger:hover {
      background-color: #d32f2f;
    }

    .test-user-status {
      margin-top: 1rem;
      padding: 0.5rem;
      font-size: 0.9rem;
      border-radius: var(--border-radius);
      display: none;
    }

    .status-success {
      background-color: #e8f5e9;
      color: #4caf50;
      display: block;
    }

    .status-error {
      background-color: #ffebee;
      color: #f44336;
      display: block;
    }
  `;

  document.head.appendChild(style);

  // Add event listeners
  const toggle = document.querySelector('.test-user-toggle');
  const content = document.querySelector('.test-user-content');

  toggle.addEventListener('click', () => {
    content.style.display = content.style.display === 'block' ? 'none' : 'block';
  });

  // Create test user
  document.getElementById('create-test-user').addEventListener('click', createTestUser);

  // Login as test user
  document.getElementById('login-test-user').addEventListener('click', loginAsTestUser);

  // Delete test user
  document.getElementById('delete-test-user').addEventListener('click', deleteTestUser);

  console.log('All test user event listeners added');
}

// Create test user
function createTestUser() {
  // Check if Firebase is available
  if (typeof firebase === 'undefined' || !firebase.auth || !firebase.firestore) {
    showStatus('Firebase is not available. Cannot create test user.', 'error');
    return;
  }

  // Check if user already exists
  firebase.auth().fetchSignInMethodsForEmail(TEST_USER.email)
    .then((signInMethods) => {
      if (signInMethods.length > 0) {
        showStatus('Test user already exists.', 'error');
        return;
      }

      // Create user
      return firebase.auth().createUserWithEmailAndPassword(TEST_USER.email, TEST_USER.password);
    })
    .then((userCredential) => {
      if (!userCredential) return;

      // Update profile with name
      return userCredential.user.updateProfile({
        displayName: TEST_USER.name
      }).then(() => {
        // Create user document in Firestore with admin privileges
        return firebase.firestore().collection('users').doc(userCredential.user.uid).set({
          name: TEST_USER.name,
          email: TEST_USER.email,
          createdAt: firebase.firestore.FieldValue.serverTimestamp(),
          groups: [],
          isAdmin: true,
          isTestUser: true
        });
      });
    })
    .then(() => {
      showStatus('Test user created successfully!', 'success');
    })
    .catch((error) => {
      console.error('Error creating test user:', error);
      showStatus(`Error: ${error.message}`, 'error');
    });
}

// Login as test user
function loginAsTestUser() {
  // Check if Firebase is available
  if (typeof firebase === 'undefined' || !firebase.auth) {
    showStatus('Firebase is not available. Cannot login as test user.', 'error');
    return;
  }

  // Check if user exists
  firebase.auth().fetchSignInMethodsForEmail(TEST_USER.email)
    .then((signInMethods) => {
      if (signInMethods.length === 0) {
        showStatus('Test user does not exist. Create it first.', 'error');
        return;
      }

      // Sign in
      return firebase.auth().signInWithEmailAndPassword(TEST_USER.email, TEST_USER.password);
    })
    .then((userCredential) => {
      if (!userCredential) return;

      showStatus('Logged in as test user!', 'success');

      // Redirect to profile page after 1 second
      setTimeout(() => {
        window.location.href = 'profile.html';
      }, 1000);
    })
    .catch((error) => {
      console.error('Error logging in as test user:', error);
      showStatus(`Error: ${error.message}`, 'error');
    });
}

// Delete test user
function deleteTestUser() {
  // Check if Firebase is available
  if (typeof firebase === 'undefined' || !firebase.auth || !firebase.firestore) {
    showStatus('Firebase is not available. Cannot delete test user.', 'error');
    return;
  }

  // Check if user is currently logged in
  const currentUser = firebase.auth().currentUser;

  if (currentUser && currentUser.email === TEST_USER.email) {
    // Delete user document from Firestore
    firebase.firestore().collection('users').doc(currentUser.uid).delete()
      .then(() => {
        // Delete user account
        return currentUser.delete();
      })
      .then(() => {
        showStatus('Test user deleted successfully!', 'success');

        // Redirect to home page after 1 second
        setTimeout(() => {
          window.location.href = 'index.html';
        }, 1000);
      })
      .catch((error) => {
        console.error('Error deleting test user:', error);
        showStatus(`Error: ${error.message}. You may need to re-authenticate.`, 'error');

        // Re-authenticate if needed
        if (error.code === 'auth/requires-recent-login') {
          firebase.auth().signOut().then(() => {
            showStatus('Please login again and then delete the test user.', 'error');

            // Redirect to login page after 2 seconds
            setTimeout(() => {
              window.location.href = 'login.html';
            }, 2000);
          });
        }
      });
  } else {
    // Find and delete the test user
    firebase.auth().fetchSignInMethodsForEmail(TEST_USER.email)
      .then((signInMethods) => {
        if (signInMethods.length === 0) {
          showStatus('Test user does not exist.', 'error');
          return;
        }

        // Need to sign in as the test user to delete it
        showStatus('You need to be logged in as the test user to delete it. Logging in...', 'error');

        // Sign in and then delete
        return firebase.auth().signInWithEmailAndPassword(TEST_USER.email, TEST_USER.password)
          .then((userCredential) => {
            // Delete user document from Firestore
            return firebase.firestore().collection('users').doc(userCredential.user.uid).delete()
              .then(() => {
                // Delete user account
                return userCredential.user.delete();
              });
          });
      })
      .then(() => {
        showStatus('Test user deleted successfully!', 'success');
      })
      .catch((error) => {
        console.error('Error deleting test user:', error);
        showStatus(`Error: ${error.message}`, 'error');
      });
  }
}

// Show status message
function showStatus(message, type) {
  const statusElement = document.getElementById('test-user-status');
  statusElement.textContent = message;
  statusElement.className = 'test-user-status';

  if (type === 'success') {
    statusElement.classList.add('status-success');
  } else if (type === 'error') {
    statusElement.classList.add('status-error');
  }
}
