// Accessibility enhancements for MindField

class AccessibilityManager {
  constructor() {
    this.setupAccessibilityFeatures();
  }

  // Setup all accessibility features
  setupAccessibilityFeatures() {
    document.addEventListener('DOMContentLoaded', () => {
      this.addAriaLabels();
      this.setupKeyboardNavigation();
      this.setupFocusManagement();
      // this.addSkipLinks(); // Removed as requested by user
      this.setupReducedMotion();
      this.improveFormAccessibility();
    });
  }

  // Add ARIA labels and roles
  addAriaLabels() {
    // Add role to main navigation
    const navbar = document.querySelector('.navbar');
    if (navbar) {
      navbar.setAttribute('role', 'navigation');
      navbar.setAttribute('aria-label', 'Main navigation');
    }

    // Add aria-labels to buttons without text
    const iconButtons = document.querySelectorAll('button:not([aria-label])');
    iconButtons.forEach(button => {
      const icon = button.querySelector('i');
      if (icon && !button.textContent.trim()) {
        // Try to determine button purpose from icon class
        if (icon.classList.contains('fa-user-circle')) {
          button.setAttribute('aria-label', 'User profile');
        } else if (icon.classList.contains('fa-times')) {
          button.setAttribute('aria-label', 'Close');
        } else if (icon.classList.contains('fa-search')) {
          button.setAttribute('aria-label', 'Search');
        }
      }
    });

    // Add aria-labels to social links
    const socialLinks = document.querySelectorAll('.social-icons a');
    socialLinks.forEach(link => {
      const icon = link.querySelector('i');
      if (icon) {
        if (icon.classList.contains('fa-facebook')) {
          link.setAttribute('aria-label', 'Follow us on Facebook');
        } else if (icon.classList.contains('fa-twitter')) {
          link.setAttribute('aria-label', 'Follow us on Twitter');
        } else if (icon.classList.contains('fa-instagram')) {
          link.setAttribute('aria-label', 'Follow us on Instagram');
        } else if (icon.classList.contains('fa-youtube')) {
          link.setAttribute('aria-label', 'Subscribe to our YouTube channel');
        }
      }
    });

    // Add role and aria-label to main content areas
    const main = document.querySelector('main');
    if (main) {
      main.setAttribute('role', 'main');
    }

    const footer = document.querySelector('footer');
    if (footer) {
      footer.setAttribute('role', 'contentinfo');
    }
  }

  // Setup keyboard navigation
  setupKeyboardNavigation() {
    // Handle hamburger menu with keyboard
    const hamburger = document.querySelector('.hamburger');
    if (hamburger) {
      hamburger.setAttribute('tabindex', '0');
      hamburger.setAttribute('role', 'button');
      hamburger.setAttribute('aria-label', 'Toggle navigation menu');
      hamburger.setAttribute('aria-expanded', 'false');

      hamburger.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          hamburger.click();
          const isExpanded = hamburger.getAttribute('aria-expanded') === 'true';
          hamburger.setAttribute('aria-expanded', !isExpanded);
        }
      });
    }

    // Handle modal keyboard navigation
    document.addEventListener('keydown', (e) => {
      const modal = document.querySelector('.modal[style*="block"]');
      if (modal && e.key === 'Escape') {
        this.closeModal(modal);
      }
    });

    // Trap focus in modals
    this.setupModalFocusTrap();
  }

  // Setup focus management
  setupFocusManagement() {
    // Add focus indicators for keyboard users
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
      }
    });

    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });

    // Manage focus for dynamic content
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // If a modal is added, focus on it
              if (node.classList && node.classList.contains('modal')) {
                this.focusModal(node);
              }
            }
          });
        }
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });
  }

  // Add skip links for screen readers
  addSkipLinks() {
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.className = 'skip-link';
    skipLink.textContent = 'Skip to main content';
    skipLink.setAttribute('aria-label', 'Skip to main content');

    document.body.insertBefore(skipLink, document.body.firstChild);

    // Add id to main content if it doesn't exist
    const main = document.querySelector('main');
    if (main && !main.id) {
      main.id = 'main-content';
    }
  }

  // Setup reduced motion preferences
  setupReducedMotion() {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleReducedMotion = (mediaQuery) => {
      if (mediaQuery.matches) {
        document.body.classList.add('reduced-motion');
        // Disable animations for users who prefer reduced motion
        const style = document.createElement('style');
        style.textContent = `
          .reduced-motion *,
          .reduced-motion *::before,
          .reduced-motion *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
            scroll-behavior: auto !important;
          }
        `;
        document.head.appendChild(style);
      } else {
        document.body.classList.remove('reduced-motion');
      }
    };

    prefersReducedMotion.addListener(handleReducedMotion);
    handleReducedMotion(prefersReducedMotion);
  }

  // Improve form accessibility
  improveFormAccessibility() {
    // Associate labels with inputs
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
      if (!input.getAttribute('aria-label') && !input.getAttribute('aria-labelledby')) {
        const label = input.closest('.form-group')?.querySelector('label');
        if (label && !label.getAttribute('for')) {
          const id = input.id || `input-${Math.random().toString(36).substr(2, 9)}`;
          input.id = id;
          label.setAttribute('for', id);
        }
      }

      // Add aria-describedby for error messages
      input.addEventListener('blur', () => {
        setTimeout(() => {
          const errorElement = input.closest('.form-group')?.querySelector('.form-error');
          if (errorElement) {
            const errorId = `error-${input.id || Math.random().toString(36).substr(2, 9)}`;
            errorElement.id = errorId;
            input.setAttribute('aria-describedby', errorId);
            input.setAttribute('aria-invalid', 'true');
          } else {
            input.removeAttribute('aria-describedby');
            input.removeAttribute('aria-invalid');
          }
        }, 100);
      });
    });

    // Add fieldset and legend for grouped form elements
    const authForms = document.querySelectorAll('.auth-form');
    authForms.forEach(form => {
      if (!form.querySelector('fieldset')) {
        const fieldset = document.createElement('fieldset');
        const legend = document.createElement('legend');
        legend.textContent = form.querySelector('h2')?.textContent || 'Form';
        legend.className = 'sr-only'; // Screen reader only
        
        fieldset.appendChild(legend);
        
        // Move form contents into fieldset
        while (form.firstChild && form.firstChild !== fieldset) {
          if (form.firstChild !== legend) {
            fieldset.appendChild(form.firstChild);
          } else {
            form.removeChild(form.firstChild);
          }
        }
        
        form.appendChild(fieldset);
      }
    });
  }

  // Setup modal focus trap
  setupModalFocusTrap() {
    document.addEventListener('keydown', (e) => {
      const modal = document.querySelector('.modal[style*="block"]');
      if (!modal || e.key !== 'Tab') return;

      const focusableElements = modal.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    });
  }

  // Focus modal when opened
  focusModal(modal) {
    const firstFocusable = modal.querySelector(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    if (firstFocusable) {
      setTimeout(() => {
        firstFocusable.focus();
      }, 100);
    }
  }

  // Close modal and return focus
  closeModal(modal) {
    modal.style.display = 'none';
    
    // Return focus to the element that opened the modal
    const triggerElement = document.querySelector('[data-modal-trigger]');
    if (triggerElement) {
      triggerElement.focus();
      triggerElement.removeAttribute('data-modal-trigger');
    }
  }

  // Announce dynamic content changes to screen readers
  announceToScreenReader(message, priority = 'polite') {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }
}

// Initialize accessibility manager
const accessibilityManager = new AccessibilityManager();

// Export for use in other modules
window.AccessibilityManager = AccessibilityManager;
window.accessibilityManager = accessibilityManager;
