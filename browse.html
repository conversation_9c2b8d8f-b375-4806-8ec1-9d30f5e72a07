<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindField - Browse</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" integrity="sha256-kLaT2GOSpHechhsozzB+flnD+zUyjE2LlfWPgU04xyI=" crossorigin="" />
</head>
<body class="browse-page">
    <header>
        <nav class="navbar">
            <div class="logo"><a href="index.html">MindField</a></div>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="about.html">About</a>
                <a href="curated-memories.html">Curated</a>
                <a href="groups.html">Groups</a>
                <a href="browse.html" class="active">Browse</a>
                <a href="contact.html">Contact</a>
            </div>
            <div class="profile-icon">
                <a href="login.html"><i class="fas fa-user-circle"></i></a>
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <div class="browse-container">
        <aside class="filters-sidebar">
            <div class="filters-header">
                <h2>Filters</h2>
                <button id="clear-filters" class="btn-text">Clear All</button>
            </div>
            <div class="filter-group">
                <h3>Dates</h3>
                <div class="date-inputs">
                    <input type="date" id="start-date" placeholder="Check in">
                    <input type="date" id="end-date" placeholder="Check out">
                </div>
            </div>
            <div class="filter-group">
                <h3>Price Range</h3>
                <div class="price-slider">
                    <input type="range" id="price-range" min="0" max="5000" step="100" value="2500">
                    <div class="price-labels">
                        <span>$0</span>
                        <span id="price-value">$2500</span>
                        <span>$5000+</span>
                    </div>
                </div>
            </div>
            <div class="filter-group">
                <h3>Retreat Type</h3>
                <div class="checkbox-group">
                    <label><input type="checkbox" value="yoga"> Yoga</label>
                    <label><input type="checkbox" value="meditation"> Meditation</label>
                    <label><input type="checkbox" value="mindfulness"> Mindfulness</label>
                    <label><input type="checkbox" value="wellness"> Wellness</label>
                    <label><input type="checkbox" value="spiritual"> Spiritual</label>
                </div>
            </div>
            <div class="filter-group">
                <h3>Duration</h3>
                <div class="checkbox-group">
                    <label><input type="checkbox" value="weekend"> Weekend (2-3 days)</label>
                    <label><input type="checkbox" value="short"> Short (4-7 days)</label>
                    <label><input type="checkbox" value="medium"> Medium (8-14 days)</label>
                    <label><input type="checkbox" value="long"> Long (15+ days)</label>
                </div>
            </div>
            <div class="filter-group">
                <h3>Amenities</h3>
                <div class="checkbox-group">
                    <label><input type="checkbox" value="pool"> Pool</label>
                    <label><input type="checkbox" value="wifi"> WiFi</label>
                    <label><input type="checkbox" value="meals"> Meals Included</label>
                    <label><input type="checkbox" value="private-room"> Private Rooms</label>
                    <label><input type="checkbox" value="nature"> Nature Access</label>
                </div>
            </div>
            <button id="apply-filters" class="btn btn-primary">Apply Filters</button>
        </aside>

        <main class="browse-content">
            <div class="browse-header">
                <h1 id="browse-title">Discover Retreats</h1>
                <div class="view-options">
                    <button id="list-view" class="view-btn active"><i class="fas fa-list"></i></button>
                    <button id="grid-view" class="view-btn"><i class="fas fa-th"></i></button>
                </div>
            </div>

            <div class="results-container">
                <div class="results-list" id="results-list">
                    <!-- Results will be populated by JavaScript -->
                </div>
                <div class="map-container" id="map">
                    <!-- Map will be initialized by JavaScript -->
                </div>
            </div>
        </main>
    </div>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <div class="logo"><a href="index.html">MindField</a></div>
                <p>Transform Your Life</p>
            </div>
            <div class="footer-links">
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="about.html">About Us</a>
                    <a href="#">Careers</a>
                    <a href="#">Press</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Blog</a>
                    <a href="#">Community</a>
                    <a href="curated-memories.html">Curated</a>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <a href="#">Help Center</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 MindField. All rights reserved.</p>
            <div class="social-icons">
                <a href="#"><i class="fab fa-facebook"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
        </div>
    </footer>

    <script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js" integrity="sha256-WBkoXOwTeyKclOHuWtc+i2uENFpDZ9YPdf5Hf+D7ewM=" crossorigin=""></script>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-storage.js"></script>

    <!-- Stripe SDK -->
    <script src="https://js.stripe.com/v3/"></script>

    <!-- App Scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/security.js"></script>
    <script src="js/performance.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/database.js"></script>
    <script src="js/payment.js"></script>
    <script src="js/main.js"></script>
    <script src="js/map.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/simple-test-user.js"></script>

    <script>
        // Initialize browse page with sample retreats
        document.addEventListener('DOMContentLoaded', function() {
            // Load retreats by default if no type specified
            const urlParams = new URLSearchParams(window.location.search);
            const browseType = urlParams.get('type') || 'retreats';

            // Load results immediately
            loadResults(browseType);
            updateBrowseTitle(browseType);

            // Add admin functionality if user is admin
            if (window.errorHandler && window.errorHandler.isAdminUser()) {
                addAdminControls();
            }
        });

        // Add admin controls for deleting retreats
        function addAdminControls() {
            // Add admin indicator
            const browseHeader = document.querySelector('.browse-header');
            if (browseHeader) {
                const adminIndicator = document.createElement('div');
                adminIndicator.className = 'admin-indicator';
                adminIndicator.innerHTML = '<i class="fas fa-shield-alt"></i> Admin Mode';
                browseHeader.appendChild(adminIndicator);
            }

            // Add delete buttons to existing retreat cards
            setTimeout(() => {
                const resultItems = document.querySelectorAll('.result-item');
                resultItems.forEach(item => {
                    addDeleteButton(item);
                });
            }, 500);
        }

        // Add delete button to a retreat card
        function addDeleteButton(resultItem) {
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'admin-delete-btn';
            deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
            deleteBtn.title = 'Delete Retreat (Admin Only)';
            deleteBtn.onclick = function(e) {
                e.stopPropagation();
                deleteRetreat(resultItem);
            };

            // Add to the result item
            resultItem.style.position = 'relative';
            resultItem.appendChild(deleteBtn);
        }

        // Delete a retreat
        function deleteRetreat(resultItem) {
            const retreatTitle = resultItem.querySelector('h3')?.textContent || 'this retreat';

            if (confirm(`Are you sure you want to delete "${retreatTitle}"? This action cannot be undone.`)) {
                // Add deletion animation
                resultItem.style.transition = 'all 0.3s ease';
                resultItem.style.opacity = '0';
                resultItem.style.transform = 'scale(0.8)';

                // Remove from DOM after animation
                setTimeout(() => {
                    resultItem.remove();
                    updateResultsCount();
                    showAdminNotification(`Deleted "${retreatTitle}" successfully`, 'success');
                }, 300);

                // Log deletion for admin
                console.log(`🗑️ Admin deleted retreat: ${retreatTitle}`);
            }
        }

        // Show admin notification
        function showAdminNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `admin-notification admin-notification-${type}`;
            notification.innerHTML = `
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            `;

            document.body.appendChild(notification);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }

        // Update results count
        function updateResultsCount() {
            const resultItems = document.querySelectorAll('.result-item');
            const browseTitle = document.getElementById('browse-title');
            if (browseTitle && resultItems.length > 0) {
                const baseTitle = browseTitle.textContent.split(' (')[0];
                browseTitle.textContent = `${baseTitle} (${resultItems.length} found)`;
            }
        }
    </script>

    <style>
        /* Admin Controls Styles */
        .admin-indicator {
            background: linear-gradient(135deg, #e53e3e, #c53030);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
        }

        .admin-delete-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #e53e3e;
            color: white;
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            z-index: 10;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .admin-delete-btn:hover {
            background: #c53030;
            transform: scale(1.1);
        }

        .admin-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            animation: slideInRight 0.3s ease-out;
            border-left: 4px solid #38a169;
        }

        .admin-notification-success i {
            color: #38a169;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Ensure result items have proper positioning for delete buttons */
        .result-item {
            position: relative !important;
        }
    </style>
</body>
</html>
