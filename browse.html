<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindField - Browse</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" integrity="sha256-kLaT2GOSpHechhsozzB+flnD+zUyjE2LlfWPgU04xyI=" crossorigin="" />
</head>
<body class="browse-page">
    <header>
        <nav class="navbar">
            <div class="logo"><a href="index.html">MindField</a></div>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="about.html">About</a>
                <a href="curated-memories.html">Curated</a>
                <a href="groups.html">Groups</a>
                <a href="browse.html" class="active">Browse</a>
                <a href="index.html#contact">Contact</a>
            </div>
            <div class="profile-icon">
                <a href="login.html"><i class="fas fa-user-circle"></i></a>
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <div class="browse-container">
        <aside class="filters-sidebar">
            <div class="filters-header">
                <h2>Filters</h2>
                <button id="clear-filters" class="btn-text">Clear All</button>
            </div>
            <div class="filter-group">
                <h3>Dates</h3>
                <div class="date-inputs">
                    <input type="date" id="start-date" placeholder="Check in">
                    <input type="date" id="end-date" placeholder="Check out">
                </div>
            </div>
            <div class="filter-group">
                <h3>Price Range</h3>
                <div class="price-slider">
                    <input type="range" id="price-range" min="0" max="5000" step="100" value="2500">
                    <div class="price-labels">
                        <span>$0</span>
                        <span id="price-value">$2500</span>
                        <span>$5000+</span>
                    </div>
                </div>
            </div>
            <div class="filter-group">
                <h3>Retreat Type</h3>
                <div class="checkbox-group">
                    <label><input type="checkbox" value="yoga"> Yoga</label>
                    <label><input type="checkbox" value="meditation"> Meditation</label>
                    <label><input type="checkbox" value="mindfulness"> Mindfulness</label>
                    <label><input type="checkbox" value="wellness"> Wellness</label>
                    <label><input type="checkbox" value="spiritual"> Spiritual</label>
                </div>
            </div>
            <div class="filter-group">
                <h3>Duration</h3>
                <div class="checkbox-group">
                    <label><input type="checkbox" value="weekend"> Weekend (2-3 days)</label>
                    <label><input type="checkbox" value="short"> Short (4-7 days)</label>
                    <label><input type="checkbox" value="medium"> Medium (8-14 days)</label>
                    <label><input type="checkbox" value="long"> Long (15+ days)</label>
                </div>
            </div>
            <div class="filter-group">
                <h3>Amenities</h3>
                <div class="checkbox-group">
                    <label><input type="checkbox" value="pool"> Pool</label>
                    <label><input type="checkbox" value="wifi"> WiFi</label>
                    <label><input type="checkbox" value="meals"> Meals Included</label>
                    <label><input type="checkbox" value="private-room"> Private Rooms</label>
                    <label><input type="checkbox" value="nature"> Nature Access</label>
                </div>
            </div>
            <button id="apply-filters" class="btn btn-primary">Apply Filters</button>
        </aside>

        <main class="browse-content">
            <div class="browse-header">
                <h1 id="browse-title">Discover Retreats</h1>
                <div class="view-options">
                    <button id="list-view" class="view-btn active"><i class="fas fa-list"></i></button>
                    <button id="grid-view" class="view-btn"><i class="fas fa-th"></i></button>
                </div>
            </div>

            <div class="results-container">
                <div class="results-list" id="results-list">
                    <!-- Results will be populated by JavaScript -->
                </div>
                <div class="map-container" id="map">
                    <!-- Map will be initialized by JavaScript -->
                </div>
            </div>
        </main>
    </div>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <div class="logo"><a href="index.html">MindField</a></div>
                <p>Transform Your Life</p>
            </div>
            <div class="footer-links">
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="about.html">About Us</a>
                    <a href="#">Careers</a>
                    <a href="#">Press</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Blog</a>
                    <a href="#">Community</a>
                    <a href="curated-memories.html">Curated</a>
                </div>
                <div class="footer-column">
                    <h3>Support</h3>
                    <a href="#">Help Center</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 MindField. All rights reserved.</p>
            <div class="social-icons">
                <a href="#"><i class="fab fa-facebook"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
        </div>
    </footer>

    <script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js" integrity="sha256-WBkoXOwTeyKclOHuWtc+i2uENFpDZ9YPdf5Hf+D7ewM=" crossorigin=""></script>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-storage.js"></script>

    <!-- Stripe SDK -->
    <script src="https://js.stripe.com/v3/"></script>

    <!-- App Scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/security.js"></script>
    <script src="js/performance.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/database.js"></script>
    <script src="js/payment.js"></script>
    <script src="js/main.js"></script>
    <script src="js/map.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/simple-test-user.js"></script>
</body>
</html>
