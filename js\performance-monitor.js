// Performance monitoring and analytics for MindField

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      pageLoad: {},
      userInteractions: [],
      errors: [],
      resources: [],
      vitals: {}
    };
    
    this.observers = {};
    this.startTime = performance.now();
    
    this.init();
  }

  // Initialize performance monitoring
  init() {
    this.measurePageLoad();
    this.measureCoreWebVitals();
    this.monitorUserInteractions();
    this.monitorResources();
    this.monitorErrors();
    this.setupReporting();
  }

  // Measure page load performance
  measurePageLoad() {
    // Navigation timing
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0];
      
      this.metrics.pageLoad = {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        domInteractive: navigation.domInteractive - navigation.navigationStart,
        firstPaint: this.getFirstPaint(),
        firstContentfulPaint: this.getFirstContentfulPaint(),
        timeToInteractive: this.estimateTimeToInteractive()
      };
      
      this.logMetric('Page Load', this.metrics.pageLoad);
    });
  }

  // Get First Paint timing
  getFirstPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : null;
  }

  // Get First Contentful Paint timing
  getFirstContentfulPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return fcp ? fcp.startTime : null;
  }

  // Estimate Time to Interactive
  estimateTimeToInteractive() {
    // Simplified TTI estimation
    const navigation = performance.getEntriesByType('navigation')[0];
    return navigation ? navigation.domInteractive - navigation.navigationStart : null;
  }

  // Measure Core Web Vitals
  measureCoreWebVitals() {
    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.metrics.vitals.lcp = lastEntry.startTime;
          this.logMetric('LCP', lastEntry.startTime);
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.lcp = lcpObserver;
      } catch (e) {
        console.warn('LCP measurement not supported');
      }

      // First Input Delay (FID)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            this.metrics.vitals.fid = entry.processingStart - entry.startTime;
            this.logMetric('FID', this.metrics.vitals.fid);
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.fid = fidObserver;
      } catch (e) {
        console.warn('FID measurement not supported');
      }

      // Cumulative Layout Shift (CLS)
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          this.metrics.vitals.cls = clsValue;
          this.logMetric('CLS', clsValue);
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.cls = clsObserver;
      } catch (e) {
        console.warn('CLS measurement not supported');
      }
    }
  }

  // Monitor user interactions
  monitorUserInteractions() {
    const interactionTypes = ['click', 'keydown', 'scroll', 'touchstart'];
    
    interactionTypes.forEach(type => {
      document.addEventListener(type, (event) => {
        const interaction = {
          type: type,
          timestamp: performance.now(),
          target: this.getElementSelector(event.target),
          duration: null
        };
        
        // Measure interaction duration for clicks
        if (type === 'click') {
          const startTime = performance.now();
          requestAnimationFrame(() => {
            interaction.duration = performance.now() - startTime;
            this.metrics.userInteractions.push(interaction);
          });
        } else {
          this.metrics.userInteractions.push(interaction);
        }
      }, { passive: true });
    });
  }

  // Monitor resource loading
  monitorResources() {
    if ('PerformanceObserver' in window) {
      try {
        const resourceObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            const resource = {
              name: entry.name,
              type: entry.initiatorType,
              size: entry.transferSize || 0,
              duration: entry.duration,
              startTime: entry.startTime
            };
            
            this.metrics.resources.push(resource);
            
            // Log slow resources
            if (entry.duration > 1000) {
              this.logMetric('Slow Resource', resource);
            }
          });
        });
        
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.resource = resourceObserver;
      } catch (e) {
        console.warn('Resource monitoring not supported');
      }
    }
  }

  // Monitor JavaScript errors
  monitorErrors() {
    // Capture JavaScript errors
    window.addEventListener('error', (event) => {
      const error = {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        timestamp: performance.now(),
        stack: event.error ? event.error.stack : null
      };
      
      this.metrics.errors.push(error);
      this.logMetric('JavaScript Error', error);
    });

    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const error = {
        message: event.reason.message || 'Unhandled Promise Rejection',
        timestamp: performance.now(),
        stack: event.reason.stack || null
      };
      
      this.metrics.errors.push(error);
      this.logMetric('Promise Rejection', error);
    });
  }

  // Setup performance reporting
  setupReporting() {
    // Report metrics periodically
    setInterval(() => {
      this.generateReport();
    }, 30000); // Every 30 seconds

    // Report on page unload
    window.addEventListener('beforeunload', () => {
      this.generateReport();
    });
  }

  // Generate performance report
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      sessionDuration: performance.now() - this.startTime,
      pageLoad: this.metrics.pageLoad,
      vitals: this.metrics.vitals,
      interactions: this.getInteractionSummary(),
      resources: this.getResourceSummary(),
      errors: this.metrics.errors.length,
      memory: this.getMemoryUsage()
    };

    this.logMetric('Performance Report', report);
    
    // Send to analytics (if configured)
    this.sendToAnalytics(report);
    
    return report;
  }

  // Get interaction summary
  getInteractionSummary() {
    const interactions = this.metrics.userInteractions;
    const summary = {
      total: interactions.length,
      byType: {},
      averageDuration: 0
    };

    interactions.forEach(interaction => {
      summary.byType[interaction.type] = (summary.byType[interaction.type] || 0) + 1;
    });

    const durationsWithValues = interactions.filter(i => i.duration !== null);
    if (durationsWithValues.length > 0) {
      summary.averageDuration = durationsWithValues.reduce((sum, i) => sum + i.duration, 0) / durationsWithValues.length;
    }

    return summary;
  }

  // Get resource summary
  getResourceSummary() {
    const resources = this.metrics.resources;
    const summary = {
      total: resources.length,
      totalSize: resources.reduce((sum, r) => sum + r.size, 0),
      byType: {},
      slowResources: resources.filter(r => r.duration > 1000).length
    };

    resources.forEach(resource => {
      summary.byType[resource.type] = (summary.byType[resource.type] || 0) + 1;
    });

    return summary;
  }

  // Get memory usage
  getMemoryUsage() {
    if (performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }
    return null;
  }

  // Get element selector for targeting
  getElementSelector(element) {
    if (!element) return 'unknown';
    
    if (element.id) return `#${element.id}`;
    if (element.className) return `.${element.className.split(' ')[0]}`;
    return element.tagName.toLowerCase();
  }

  // Log metric to console
  logMetric(name, data) {
    if (window.ENV_CONFIG && window.ENV_CONFIG.isDevelopment) {
      console.log(`📊 ${name}:`, data);
    }
  }

  // Send to analytics service
  sendToAnalytics(data) {
    // This would integrate with your analytics service
    // For now, just store in localStorage for debugging
    try {
      const existingData = JSON.parse(localStorage.getItem('mindfield_performance') || '[]');
      existingData.push(data);
      
      // Keep only last 10 reports
      if (existingData.length > 10) {
        existingData.splice(0, existingData.length - 10);
      }
      
      localStorage.setItem('mindfield_performance', JSON.stringify(existingData));
    } catch (e) {
      console.warn('Could not store performance data:', e);
    }
  }

  // Get stored performance data
  getStoredData() {
    try {
      return JSON.parse(localStorage.getItem('mindfield_performance') || '[]');
    } catch (e) {
      return [];
    }
  }

  // Clear stored data
  clearStoredData() {
    localStorage.removeItem('mindfield_performance');
  }

  // Measure specific operation
  measureOperation(name, operation) {
    const startTime = performance.now();
    
    const result = operation();
    
    // Handle both sync and async operations
    if (result && typeof result.then === 'function') {
      return result.then(value => {
        const duration = performance.now() - startTime;
        this.logMetric(`Operation: ${name}`, { duration, async: true });
        return value;
      });
    } else {
      const duration = performance.now() - startTime;
      this.logMetric(`Operation: ${name}`, { duration, async: false });
      return result;
    }
  }

  // Disconnect all observers
  disconnect() {
    Object.values(this.observers).forEach(observer => {
      if (observer && observer.disconnect) {
        observer.disconnect();
      }
    });
  }
}

// Initialize performance monitor
const performanceMonitor = new PerformanceMonitor();

// Export for use in other modules
window.PerformanceMonitor = PerformanceMonitor;
window.performanceMonitor = performanceMonitor;
